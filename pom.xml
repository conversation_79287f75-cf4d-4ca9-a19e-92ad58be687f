<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.jqsoft</groupId>
        <artifactId>zhmz-platform-parent</artifactId>
        <version>1.0.5</version>
    </parent>
    <artifactId>zhmz-cloudplatform-msgs</artifactId>
    <version>1.0.0</version>

    <dependencies>
        <!-- zhmz-common 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>zhmz-common</artifactId>
            <!--加上版本号测试环境会构建失败-->
            <!--<version>1.0.0</version>-->
        </dependency>
        <!-- 多数据源 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boot-starter-multids</artifactId>
            <!--<exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
            </exclusions>-->
        </dependency>
        <!-- redis 组件 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.oltu.oauth2</groupId>
            <artifactId>org.apache.oltu.oauth2.client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.51</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <!-- jqboot api 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-apis</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- jqboot service 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-service</artifactId>
            <version>2.6.8</version>
        </dependency>
        <!-- jqboot datasource 组件 - 注释掉，因为已使用多数据源配置 -->
        <!--<dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boot-starter-druid</artifactId>
        </dependency>-->
        <!-- jq persist 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boot-starter-mybatisplus3</artifactId>
            <version>2.6.5.5</version>
        </dependency>
        <!-- jqboot扩展组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boot-starter-patch</artifactId>
            <version>2.5.6</version>
        </dependency>
        <!-- jq excel 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boot-starter-excel</artifactId>
            <version>2.5.2</version>
        </dependency>
        <!-- jqboot observe 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boot-starter-observe</artifactId>
            <version>2.5.0</version>
        </dependency>
        <!-- sso 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-boss-auth-client</artifactId>
            <version>1.0.5</version>
        </dependency>
        <!-- fb 组件 -->
        <dependency>
            <groupId>org.jsets</groupId>
            <artifactId>fb-common</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.jsets</groupId>
            <artifactId>fb-security</artifactId>
            <version>0.1.5.3</version>
        </dependency>
        <!-- dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>
        <!-- mysql 驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- 神通数据库驱动 -->
        <!--        <dependency>-->
        <!--            <groupId>com.oscar</groupId>-->
        <!--            <artifactId>oscarJDBC16</artifactId>-->
        <!--            <version>1</version>-->
        <!--        </dependency>-->
        <!-- 达梦数据库驱动(dm8) -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.3.62</version>
        </dependency>
        <!-- spring cloud -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <!-- spring cloud alibaba -->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!-- spring cloud netflix -->
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.wst.gateway.sdk</groupId>
            <artifactId>wst-gateway-sdk-java</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.149</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.4.0</version>
        </dependency>

        <!-- zhmz-common 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-core-common</artifactId>
            <version>2.6.8</version>
        </dependency>
        <!-- jqboot api 组件 -->
        <dependency>
            <groupId>net.jqsoft</groupId>
            <artifactId>jq-apis</artifactId>
            <version>2.7.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.15.0</version>
        </dependency>
        <!--文档操作-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.0</version>
        </dependency>
        <!--  上面需要的依赖-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>15.8.0</version>
        </dependency>

        <!-- iTextPDF -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>

        <!-- iTextPDF -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.18</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <!-- ZXing 核心库：
 提供二维码的编码（生成）和解码（解析）功能，包括常见的一维码、二维码格式 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.3</version>
        </dependency>

        <!-- ZXing Java SE 支持库：
             提供图像相关的处理（如 BitMatrix 转换成 BufferedImage、写 PNG 文件、输出流等）；
             依赖 Java AWT 和 Java ImageIO，是 core 的补充组件 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.3</version>
        </dependency>

        <!-- Apache CXF WebService 支持 -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.4.5</version>
        </dependency>

        <!-- sa-token OAuth2 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>1.42.0</version>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-oauth2</artifactId>
            <version>1.42.0</version>
        </dependency>

        <!-- 国密SM4加密算法 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.70</version>
        </dependency>

        <!-- MZT电信国密改造-数据库加密插件 -->
        <dependency>
            <groupId>com.zdxlz.dplugin</groupId>
            <artifactId>d-plugin</artifactId>
            <version>1.2.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>net.jqsoft.zhmz.cloudplatform.msgs.Application</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>nexus-snapshots</id>
            <name>public nexus snapshots server</name>
            <url>http://10.1.171.18:8010/repository/maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>nexus-public</id>
            <name>public nexus server</name>
            <url>http://10.1.171.18:8010/repository/maven-releases/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

</project>