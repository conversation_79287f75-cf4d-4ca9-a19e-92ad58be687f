# 参数传递解决方案说明

## 问题描述

在电子签章系统中，存在以下问题：
1. **第一次调用 `/view` 接口**：前端传来参数 + 后端查询数据，生成Word预览
2. **第二次调用 `/batchSignature` 接口**：生成带电子签章的新Word，但前端参数丢失

## 解决方案概述

采用**预览参数保存机制**，在预览时将前端参数保存到数据库，在签章时自动恢复这些参数。

### 核心机制

1. **预览时保存参数**：在 `/view` 接口调用时，将所有前端参数保存到 `msgs_signaturehistory` 表的预览记录中
2. **签章时恢复参数**：在 `/batchSignature` 接口调用时，如果没有传递参数，则从预览记录中自动恢复
3. **参数优先级**：当前传入参数 > 历史签章记录参数 > 预览记录参数

## 技术实现

### 1. 数据库字段

`msgs_signaturehistory` 表已有以下字段支持参数保存：

```sql
-- 业务参数字段（用于正式签章记录）
business_params TEXT COMMENT '业务参数（JSON格式存储）',

-- 预览参数字段（用于预览记录）
preview_params TEXT COMMENT '预览时的动态参数（JSON格式存储）',
is_preview_record INT DEFAULT 0 COMMENT '是否为预览记录（0：正式签章记录，1：预览记录）'
```

### 2. Controller层改动

#### `/view` 接口增强

```java
@PostMapping(value = "/view")
public MsgRespond getView(
        @RequestParam(name = "familyInfoIds", required = true) String familyInfoIds,
        @RequestParam(name = "templateId", required = true) String templateId,
        @RequestParam(name = "fileType", required = true) String fileType,
        @RequestParam(name = "stampDate", required = false) String stampDate,
        @RequestParam(name = "publicStartDate", required = false) String publicStartDate,
        @RequestParam(name = "publicEndDate", required = false) String publicEndDate,
        @RequestParam(name = "outReason", required = false) String outReason,
        @RequestParam(name = "auditDesc", required = false) String auditDesc,
        @RequestParam(name = "adjustReason", required = false) String adjustReason,
        HttpServletRequest request) {
    
    // 收集所有前端参数（包括动态参数）
    Map<String, Object> allFrontendParams = collectAllFrontendParams(request, outReason, auditDesc, adjustReason);
    
    // 使用支持动态参数的预览方法（会自动保存预览参数）
    List<ElectronicSignatureVO> result = service.getViewWithDynamicParams(
            familyInfoIds, templateId, fileType, stampDateParsed, 
            publicStartDateParsed, publicEndDateParsed, allFrontendParams);
    
    return listRespond(result);
}
```

#### `/batchSignature` 接口增强

```java
@PostMapping(value = "/batchSignature")
public MsgRespond batchGetSignature(String familyInfoIds, String templateId, String fileType, String stampDate,
                                  String keyword, String outReason, String auditDesc, String adjustReason,
                                  HttpServletRequest request) throws IOException {
    
    // 收集所有业务参数（包括动态参数）
    Map<String, Object> businessParams = collectBusinessParams(request, outReason, auditDesc, adjustReason);

    // 如果当前请求没有业务参数，尝试从预览记录中恢复
    if (businessParams.isEmpty()) {
        businessParams = recoverParamsFromPreview(familyInfoIds, fileType);
        if (!businessParams.isEmpty()) {
            log.info("从预览记录中恢复业务参数：{}", businessParams);
        }
    }
    
    // 使用支持业务参数的签章方法
    return listRespond(service.batchSignature(userVO, familyInfoIds, templateId, fileType, dataParams, keyword, businessParams));
}
```

### 3. Service层实现

#### 预览参数保存

```java
private void savePreviewParams(String familyId, String templateId, String fileType, Map<String, Object> dynamicParams) {
    try {
        UserVO userVO = SecurityUtils.getAccount();
        
        // 查询是否已存在预览记录
        MsgsSignaturehistory existingPreview = msgsSignaturehistoryService.selectOne(
                new LambdaQueryWrapper<MsgsSignaturehistory>()
                        .eq(MsgsSignaturehistory::getFamilyId, familyId)
                        .eq(MsgsSignaturehistory::getDocumentType, Integer.parseInt(fileType))
                        .eq(MsgsSignaturehistory::getIsPreviewRecord, MsgsSignaturehistory.RECORD_TYPE_PREVIEW)
                        .eq(MsgsSignaturehistory::getDeleteMark, 0)
        );

        if (existingPreview != null) {
            // 更新现有预览记录
            existingPreview.setPreviewParamsFromMap(dynamicParams);
            existingPreview.setModifyDate(new Date());
            existingPreview.setModifyUserId(userVO.getId());
            existingPreview.setModifyUserName(userVO.getRealName());
            msgsSignaturehistoryService.updateById(existingPreview);
            log.info("更新预览参数记录，家庭ID：{}, 模板ID：{}, 参数：{}", familyId, templateId, dynamicParams);
        } else {
            // 创建新的预览记录
            MsgsSignaturehistory previewRecord = new MsgsSignaturehistory();
            previewRecord.setFamilyId(familyId);
            previewRecord.setDocumentType(Integer.parseInt(fileType));
            previewRecord.setIsPreviewRecord(MsgsSignaturehistory.RECORD_TYPE_PREVIEW);
            previewRecord.setPreviewParamsFromMap(dynamicParams);
            previewRecord.setSignatureLevel(1);
            previewRecord.setDeleteMark(0);
            previewRecord.setCreateDate(new Date());
            previewRecord.setCreateUserId(userVO.getId());
            previewRecord.setCreateUserName(userVO.getRealName());

            msgsSignaturehistoryService.save(previewRecord);
            log.info("保存新的预览参数记录，家庭ID：{}, 模板ID：{}, 参数：{}", familyId, templateId, dynamicParams);
        }
    } catch (Exception e) {
        log.error("保存预览参数失败，家庭ID：{}, 模板ID：{}, 参数：{}", familyId, templateId, dynamicParams, e);
        // 不抛出异常，避免影响预览功能
    }
}
```

#### 预览参数恢复

```java
private Map<String, Object> getPreviewParams(String familyId, String fileType) {
    try {
        MsgsSignaturehistory previewRecord = msgsSignaturehistoryService.selectOne(
                new LambdaQueryWrapper<MsgsSignaturehistory>()
                        .eq(MsgsSignaturehistory::getFamilyId, familyId)
                        .eq(MsgsSignaturehistory::getDocumentType, Integer.parseInt(fileType))
                        .eq(MsgsSignaturehistory::getIsPreviewRecord, MsgsSignaturehistory.RECORD_TYPE_PREVIEW)
                        .eq(MsgsSignaturehistory::getDeleteMark, 0)
                        .orderByDesc(MsgsSignaturehistory::getCreateDate)
                        .last("LIMIT 1")
        );

        if (previewRecord != null && previewRecord.hasPreviewParams()) {
            Map<String, Object> params = previewRecord.getPreviewParamsMap();
            log.info("从预览记录恢复动态参数，家庭ID：{}, 文件类型：{}, 参数：{}", familyId, fileType, params);
            return params;
        }
    } catch (Exception e) {
        log.error("获取预览参数失败，家庭ID：{}, 文件类型：{}", familyId, fileType, e);
    }

    return new HashMap<>();
}
```

## 参数收集机制

### 前端参数收集

支持多种参数传递方式：

1. **传统参数**：`outReason`, `auditDesc`, `adjustReason`
2. **动态参数**：以 `businessParam_` 开头的参数，如 `businessParam_auditor=张三`
3. **直接参数**：常用的业务参数，如 `auditor`, `confirmer`, `helpMoney` 等

```java
private Map<String, Object> collectAllFrontendParams(HttpServletRequest request, String outReason, String auditDesc, String adjustReason) {
    Map<String, Object> frontendParams = new HashMap<>();

    // 添加传统业务参数
    if (StringUtils.isNotBlank(outReason)) {
        frontendParams.put("outReason", outReason);
    }
    if (StringUtils.isNotBlank(auditDesc)) {
        frontendParams.put("auditDesc", auditDesc);
    }
    if (StringUtils.isNotBlank(adjustReason)) {
        frontendParams.put("adjustReason", adjustReason);
    }

    // 收集动态参数
    Enumeration<String> paramNames = request.getParameterNames();
    while (paramNames.hasMoreElements()) {
        String paramName = paramNames.nextElement();
        if (paramName.startsWith("businessParam_")) {
            String actualParamName = paramName.substring("businessParam_".length());
            String paramValue = request.getParameter(paramName);
            if (StringUtils.isNotBlank(actualParamName) && StringUtils.isNotBlank(paramValue)) {
                frontendParams.put(actualParamName, paramValue);
            }
        }
    }

    // 收集其他常用前端参数
    String[] otherFrontendParams = {"auditor", "confirmer", "helpMoney", "approvalAmount", "approverName"};
    for (String paramName : otherFrontendParams) {
        String paramValue = request.getParameter(paramName);
        if (StringUtils.isNotBlank(paramValue)) {
            frontendParams.put(paramName, paramValue);
        }
    }

    return frontendParams;
}
```

## 使用示例

### 前端调用示例

#### 1. 预览接口调用

```javascript
// 第一次调用 - 预览
const previewParams = {
    familyInfoIds: "1001,1002",
    templateId: "dibao_approval", 
    fileType: "1",
    stampDate: "2025-09-25",
    outReason: "户籍迁移",
    auditDesc: "经审核符合条件",
    adjustReason: "金额调整",
    // 动态参数
    businessParam_auditor: "张三",
    businessParam_confirmer: "李四",
    businessParam_helpMoney: "500.00"
};

fetch('/msgs_signaturehistory/view', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: new URLSearchParams(previewParams)
});
```

#### 2. 签章接口调用

```javascript
// 第二次调用 - 签章（不需要传递业务参数，会自动从预览记录中恢复）
const signatureParams = {
    familyInfoIds: "1001,1002",
    templateId: "dibao_approval",
    fileType: "1", 
    keyword: "乡镇级"
};

fetch('/msgs_signaturehistory/batchSignature', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: new URLSearchParams(signatureParams)
});
```

## 优势

1. **无缝兼容**：现有的前端代码无需修改，系统会自动处理参数传递
2. **灵活扩展**：支持动态参数，可以轻松添加新的业务参数
3. **数据一致性**：确保预览和签章使用相同的参数，避免数据不一致
4. **容错性强**：即使预览记录丢失，系统仍能正常工作
5. **可追溯性**：所有参数变化都有记录，便于问题排查

## 注意事项

1. **参数优先级**：当前传入参数 > 历史签章记录参数 > 预览记录参数
2. **数据清理**：预览记录会随着时间积累，建议定期清理过期的预览记录
3. **参数验证**：所有参数都会经过验证，确保数据安全性
4. **日志记录**：详细的日志记录便于问题排查和系统监控
