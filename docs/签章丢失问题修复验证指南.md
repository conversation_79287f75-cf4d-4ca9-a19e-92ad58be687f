# 签章丢失问题修复验证指南

## 问题描述

### 问题1：跨级签章丢失问题
在区县进行确认签章时，发现第一步的乡镇审核签章丢失。

### 问题2：业务参数丢失问题（新增）
在生成第一个Word文书模板时，前端传来的注销理由（停保原因）会绑定到Word里面，但再次点击电子签章时，这个前端传来的停保原因就丢掉了。

## 问题根本原因

### 跨级签章丢失原因
1. **查询逻辑问题**：`getSignatureHistory` 方法使用当前用户的区划编码查询签章记录，区县用户无法查询到乡镇级别的签章记录
2. **更新逻辑问题**：区县确认时会覆盖或删除乡镇的签章记录
3. **缺乏保护机制**：没有防止跨级删除签章的保护机制

### 业务参数丢失原因（新增）
1. **参数传递正常**：前端传递的停保原因等业务参数能够正确传递到后端
2. **模板生成正常**：业务参数能够正确绑定到Word模板中
3. **参数保存缺失**：在保存签章历史记录时，业务参数没有被持久化到数据库
4. **参数恢复缺失**：再次签章时，没有从历史记录中恢复业务参数

## 修复方案

### A. 跨级签章丢失修复

#### 1. 修复签章历史查询逻辑
- **文件**：`MsgsSignaturehistoryServiceImpl.java`
- **方法**：`getSignatureHistory`
- **修复内容**：
  - 首先查询当前区划的签章记录
  - 如果当前区划没有记录且是区县级别，则查询下级乡镇的签章记录
  - 使用 `likeRight` 查询所有以区县编码开头的签章记录

#### 2. 修复签章更新逻辑
- **文件**：`MsgsSignaturehistoryServiceImpl.java`
- **方法**：`updateSignatureHistory`
- **修复内容**：
  - 判断是否为不同级别的签章（通过区划编码判断）
  - 如果是不同级别，创建新的签章记录而不是更新原有记录
  - 使用ID精确更新，避免误更新其他记录

#### 3. 修复取消签章逻辑
- **文件**：`MsgsSignaturehistoryServiceImpl.java`
- **方法**：`cancelSignature`
- **修复内容**：
  - 添加区划编码条件，只删除当前用户区划级别的签章记录
  - 检查受保护的签章记录，跳过删除

#### 4. 添加签章保护机制
- **文件**：`MsgsSignaturehistory.java`
- **新增内容**：
  - `isProtected()` 方法：判断签章是否受保护
  - `getSignatureLevelDescription()` 方法：获取签章级别描述
  - 保护机制常量定义

#### 5. 增强查询和显示功能
- **新增接口**：`/msgs_signaturehistory/getAllLevelSignatures`
- **功能**：查询家庭的所有级别签章记录
- **新增接口**：`/msgs_signaturehistory/validateSignatureOperation`
- **功能**：验证签章操作的安全性

### B. 业务参数丢失修复（新增）

#### 1. 修复签章历史记录保存逻辑
- **文件**：`MsgsSignaturehistoryServiceImpl.java`
- **方法**：`processSignatureWithBusinessParams`
- **修复内容**：
  - 当存在历史记录时，从历史记录中恢复业务参数
  - 添加`shouldRegeneratePdfForBusinessParams`方法，判断是否需要重新生成PDF
  - 确保业务参数在签章过程中不会丢失

#### 2. 修复签章记录查询逻辑
- **文件**：`MsgsSignaturehistoryServiceImpl.java`
- **方法**：`generatePdfForPreview`
- **修复内容**：
  - 从签章记录中恢复业务参数的逻辑已存在
  - 确保预览功能也能正确显示业务参数

#### 3. 添加业务参数验证和日志
- **文件**：`MsgsSignaturehistoryController.java`
- **修复内容**：
  - 添加`validateBusinessParams`方法验证业务参数长度
  - 增强日志记录，便于问题排查
  - 添加`getBusinessParams`接口用于调试

## 验证步骤

### A. 跨级签章丢失验证

#### 步骤1：乡镇审核签章
```bash
# 使用乡镇用户登录，进行签章操作
POST /msgs_signaturehistory/batchSignature
{
    "familyInfoIds": "family_001",
    "templateId": "template_001",
    "fileType": "1",
    "stampDate": "2025-01-15",
    "keyword": "乡镇审核"
}
```

#### 步骤2：验证乡镇签章记录
```bash
# 查询所有级别的签章记录
GET /msgs_signaturehistory/getAllLevelSignatures?familyId=family_001&documentType=1
```
**预期结果**：能查询到乡镇的签章记录

#### 步骤3：区县确认签章
```bash
# 使用区县用户登录，进行签章操作
POST /msgs_signaturehistory/batchSignature
{
    "familyInfoIds": "family_001",
    "templateId": "template_001",
    "fileType": "1",
    "stampDate": "2025-01-15",
    "keyword": "区县确认"
}
```

#### 步骤4：验证两级签章都存在
```bash
# 再次查询所有级别的签章记录
GET /msgs_signaturehistory/getAllLevelSignatures?familyId=family_001&documentType=1
```
**预期结果**：能同时查询到乡镇和区县的签章记录

### B. 业务参数丢失验证（新增）

#### 步骤1：验证业务参数保存
```bash
# 生成第一次签章（包含业务参数）
POST /msgs_signaturehistory/batchSignature
{
    "familyInfoIds": "test_family_001",
    "templateId": "dibao_approval",
    "fileType": "1",
    "outReason": "户籍迁移",
    "auditDesc": "经审核符合条件",
    "adjustReason": "金额调整"
}
```

#### 步骤2：查询业务参数是否保存
```bash
# 查询签章记录的业务参数
GET /msgs_signaturehistory/getBusinessParams?familyId=test_family_001&documentType=1
```
**预期结果**：
```json
{
  "code": 200,
  "data": {
    "familyId": "test_family_001",
    "documentType": 1,
    "signatureCount": 1,
    "signatures": [{
      "outReason": "户籍迁移",
      "auditDesc": "经审核符合条件",
      "adjustReason": "金额调整",
      "hasBusinessParams": true
    }]
  }
}
```

#### 步骤3：验证业务参数恢复
```bash
# 进行第二次签章（不传业务参数）
POST /msgs_signaturehistory/batchSignature
{
    "familyInfoIds": "test_family_001",
    "templateId": "dibao_approval",
    "fileType": "1",
    "keyword": "县级审批"
}
```
**预期结果**：查看日志应包含业务参数恢复信息：
```
业务参数恢复情况 - 停保原因: null -> 户籍迁移, 审核描述: null -> 经审核符合条件, 调整原因: null -> 金额调整
```

#### 步骤4：验证预览功能
```bash
# 生成预览
POST /msgs_signaturehistory/view
{
    "familyInfoIds": "test_family_001",
    "templateId": "dibao_approval",
    "fileType": "pdf"
}
```
**预期结果**：生成的PDF中应包含停保原因等业务参数

#### 步骤5：验证参数变更
```bash
# 使用新的业务参数进行签章
POST /msgs_signaturehistory/batchSignature
{
    "familyInfoIds": "test_family_001",
    "templateId": "dibao_approval",
    "fileType": "1",
    "outReason": "收入变化"
}
```
**预期结果**：查看日志应包含PDF重新生成信息：
```
检测到业务参数变化，重新生成PDF文件
```

### C. 保护机制验证

#### 步骤1：验证签章操作安全性
```bash
# 验证删除操作的安全性
GET /msgs_signaturehistory/validateSignatureOperation?familyId=family_001&documentType=1&operation=DELETE
```
**预期结果**：如果存在其他级别的签章，应返回警告信息

#### 步骤2：尝试取消签章
```bash
# 尝试取消签章
POST /msgs_signaturehistory/cancelSignature
{
    "familyInfoIds": "family_001",
    "templateId": "template_001",
    "fileType": "1"
}
```
**预期结果**：只删除当前用户级别的签章，不影响其他级别的签章

### D. 日志验证

查看应用日志，确认以下关键日志信息：

1. **跨级查询日志**：
   ```
   当前区划无签章记录，尝试查询下级区划的签章记录，区县编码：123456
   查询下级区划签章记录，找到 X 条记录
   ```

2. **不同级别签章日志**：
   ```
   检测到不同级别的签章（历史：123456789012，当前：123456000000），将创建新的签章记录
   ```

3. **保护机制日志**：
   ```
   发现 X 条受保护的签章记录，将跳过删除
   受保护签章记录：ID=xxx, 级别=二级签章, 关键字=xxx, 区划=xxx
   ```

4. **业务参数相关日志**（新增）：
   ```
   业务参数恢复情况 - 停保原因: null -> 户籍迁移, 审核描述: null -> 经审核符合条件, 调整原因: null -> 金额调整
   检测到业务参数变化，重新生成PDF文件
   业务参数未发生变化，无需重新生成PDF
   使用业务参数重新生成PDF - 停保原因：户籍迁移, 审核描述：经审核符合条件, 调整原因：金额调整
   ```

## 测试用例

### 单元测试
- **文件**：`MsgsSignaturehistoryServiceTest.java`
- **测试内容**：
  - 跨级查询签章历史记录
  - 签章保护机制
  - 签章级别描述
  - 不同级别签章不会相互覆盖

### 集成测试
- 完整的签章流程测试
- API接口功能测试
- 数据库操作验证

## 预期效果

### 跨级签章丢失修复效果
1. **乡镇审核签章不会丢失**：区县确认时能正确显示和保留乡镇的签章
2. **多级签章共存**：支持乡镇、区县等多个级别的签章同时存在
3. **操作安全性**：防止误删除其他级别的签章记录
4. **可追溯性**：提供完整的签章历史查询功能

### 业务参数丢失修复效果（新增）
1. **参数持久化**：业务参数能够正确保存到签章历史记录中
2. **参数恢复**：再次签章时能够从历史记录中恢复业务参数
3. **智能重生成**：当业务参数发生变化时，自动重新生成PDF
4. **参数传承**：多级签章过程中，业务参数能够正确传承
5. **调试支持**：提供接口查询业务参数，便于问题排查

## 注意事项

1. **数据库兼容性**：修复使用了DM数据库兼容的SQL语法
2. **性能考虑**：跨级查询可能会增加数据库查询次数，建议在生产环境中监控性能
3. **权限控制**：确保用户只能操作自己权限范围内的签章记录
4. **备份建议**：在部署修复代码前，建议备份签章相关的数据表
5. **数据库字段**（新增）：确保`msgs_signaturehistory`表包含`out_reason`、`audit_desc`、`adjust_reason`字段
6. **参数长度限制**（新增）：业务参数有长度限制，停保原因和调整原因不超过500字符，审核描述不超过1000字符
7. **向下兼容**（新增）：修复保持向下兼容，不影响现有功能
8. **日志级别**（新增）：建议将相关日志级别设置为INFO，便于生产环境排查问题

## 回滚方案

如果修复后出现问题，可以通过以下步骤回滚：

1. 恢复原始的 `MsgsSignaturehistoryServiceImpl.java` 文件
2. 删除新增的API接口
3. 恢复数据库备份（如有必要）
4. 重启应用服务
