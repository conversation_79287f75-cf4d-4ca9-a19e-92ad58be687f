# 接口参数优化说明

## 优化背景

在实现了参数自动恢复机制后，`/batchSignature` 接口的传统业务参数（`outReason`, `auditDesc`, `adjustReason`）变得冗余，因为系统已经能够自动从预览记录中恢复这些参数。

## 优化内容

### 1. 主接口简化

**优化前：**
```java
@PostMapping(value = "/batchSignature")
public MsgRespond batchGetSignature(String familyInfoIds, String templateId, String fileType, String stampDate,
                                  String keyword, String outReason, String auditDesc, String adjustReason,
                                  HttpServletRequest request) throws IOException
```

**优化后：**
```java
@PostMapping(value = "/batchSignature")
public MsgRespond batchGetSignature(String familyInfoIds, String templateId, String fileType, String stampDate,
                                  String keyword, HttpServletRequest request) throws IOException
```

### 2. 向后兼容支持

为了确保现有系统的兼容性，我们提供了一个废弃的兼容接口：

```java
@PostMapping(value = "/batchSignatureLegacy")
@Deprecated
public MsgRespond batchGetSignatureLegacy(String familyInfoIds, String templateId, String fileType, String stampDate,
                                        String keyword, String outReason, String auditDesc, String adjustReason,
                                        HttpServletRequest request) throws IOException
```

## 参数处理逻辑

### 新版本接口逻辑

1. **收集动态参数**：从 `HttpServletRequest` 中收集所有业务参数
2. **自动参数恢复**：如果没有传递参数，自动从预览记录中恢复
3. **参数优先级**：当前传入参数 > 历史签章记录参数 > 预览记录参数

```java
// 收集所有业务参数（包括动态参数）
Map<String, Object> businessParams = collectBusinessParams(request, null, null, null);

// 如果当前请求没有业务参数，尝试从预览记录中恢复
if (businessParams.isEmpty()) {
    businessParams = recoverParamsFromPreview(familyInfoIds, fileType);
    if (!businessParams.isEmpty()) {
        log.info("从预览记录中恢复业务参数：{}", businessParams);
    }
}
```

### 兼容版本接口逻辑

兼容版本会将传统参数转换为动态参数，然后调用新版本接口：

```java
// 将传统参数添加到动态参数中
Map<String, String> additionalParams = new HashMap<>();
if (outReason != null) additionalParams.put("outReason", outReason);
if (auditDesc != null) additionalParams.put("auditDesc", auditDesc);
if (adjustReason != null) additionalParams.put("adjustReason", adjustReason);

// 创建包装的request，添加传统参数
HttpServletRequestWrapper wrappedRequest = new HttpServletRequestWrapper(request) {
    @Override
    public String getParameter(String name) {
        if (additionalParams.containsKey(name)) {
            return additionalParams.get(name);
        }
        return super.getParameter(name);
    }
};

// 调用新版本接口
return batchGetSignature(familyInfoIds, templateId, fileType, stampDate, keyword, wrappedRequest);
```

## 前端调用方式

### 推荐方式（新版本）

```javascript
// 签章接口调用 - 不需要传递业务参数，会自动从预览记录中恢复
const signatureParams = {
    familyInfoIds: "1001,1002",
    templateId: "dibao_approval",
    fileType: "1", 
    keyword: "乡镇级"
};

fetch('/msgs_signaturehistory/batchSignature', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: new URLSearchParams(signatureParams)
});
```

### 兼容方式（如果需要覆盖参数）

```javascript
// 如果需要在签章时覆盖预览时的参数，可以通过动态参数传递
const signatureParams = {
    familyInfoIds: "1001,1002",
    templateId: "dibao_approval",
    fileType: "1", 
    keyword: "乡镇级",
    // 动态参数方式
    businessParam_outReason: "新的停保原因",
    businessParam_auditDesc: "新的审核描述"
};

fetch('/msgs_signaturehistory/batchSignature', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: new URLSearchParams(signatureParams)
});
```

### 传统方式（不推荐，但仍支持）

```javascript
// 使用废弃的兼容接口
const signatureParams = {
    familyInfoIds: "1001,1002",
    templateId: "dibao_approval",
    fileType: "1", 
    keyword: "乡镇级",
    outReason: "户籍迁移",
    auditDesc: "经审核符合条件",
    adjustReason: "金额调整"
};

fetch('/msgs_signaturehistory/batchSignatureLegacy', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: new URLSearchParams(signatureParams)
});
```

## 优化优势

### 1. 接口简化
- 减少了3个冗余参数
- 接口更加简洁，易于理解和维护

### 2. 自动化程度提高
- 完全自动的参数恢复机制
- 减少前端传参的复杂性

### 3. 向后兼容
- 现有系统无需立即修改
- 提供平滑的迁移路径

### 4. 灵活性增强
- 支持动态参数覆盖
- 支持更复杂的业务场景

## 迁移建议

### 短期（立即可用）
- 新版本接口已经可以使用
- 现有调用方式仍然支持

### 中期（建议迁移）
- 逐步将前端调用迁移到新版本接口
- 移除传统参数的传递

### 长期（完全优化）
- 移除兼容接口
- 完全基于自动参数恢复机制

## 注意事项

1. **参数覆盖**：如果在签章时需要覆盖预览时的参数，使用动态参数方式传递
2. **日志监控**：系统会记录参数恢复的详细日志，便于问题排查
3. **兼容性**：兼容接口会在日志中产生警告信息，提醒升级到新版本
4. **测试验证**：建议在测试环境充分验证新接口的功能

## 总结

这次优化大大简化了签章接口的使用，同时保持了完全的向后兼容性。通过自动参数恢复机制，系统变得更加智能和易用，减少了前端开发的复杂性，提高了整体的用户体验。
