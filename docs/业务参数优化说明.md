# 业务参数优化说明

## 优化背景

原来的业务参数存储方式使用了多个独立字段：
- `outReason` - 停保原因
- `auditDesc` - 审核描述
- `adjustReason` - 调整原因
- `businessParams` - 业务参数JSON

这种方式存在以下问题：
1. **数据冗余**：同样的信息存储在多个字段中
2. **维护复杂**：需要同时维护多个字段的一致性
3. **扩展性差**：新增业务参数需要修改数据库表结构
4. **灵活性不足**：无法支持前端动态传递的参数

## 优化方案

### 1. 统一存储方式

**优化后**：所有业务参数统一存储在`businessParams`字段中，采用JSON格式，支持动态参数：

```json
{
  "outReason": "户籍迁移",
  "auditDesc": "经审核符合条件",
  "adjustReason": "金额调整",
  "customParam1": "自定义参数1",
  "customParam2": "自定义参数2"
}
```

### 2. 动态参数支持

前端可以通过以下方式传递动态业务参数：

1. **固定参数**：`outReason`、`auditDesc`、`adjustReason`（向下兼容）
2. **动态参数**：以`businessParam_`为前缀的参数，如：
   - `businessParam_customField1=值1`
   - `businessParam_customField2=值2`
   - `businessParam_specialReason=特殊原因`

### 3. 实体类优化

#### 2.1 `hasBusinessParams()` 方法优化

**优化前**：
```java
public boolean hasBusinessParams() {
    return (this.outReason != null && !this.outReason.trim().isEmpty()) ||
           (this.auditDesc != null && !this.auditDesc.trim().isEmpty()) ||
           (this.adjustReason != null && !this.adjustReason.trim().isEmpty()) ||
           (this.businessParams != null && !this.businessParams.trim().isEmpty());
}
```

**优化后**：
```java
public boolean hasBusinessParams() {
    return this.businessParams != null && !this.businessParams.trim().isEmpty();
}
```

#### 2.2 `getBusinessParamsSummary()` 方法优化

**优化前**：需要检查多个字段并拼接摘要

**优化后**：
```java
public String getBusinessParamsSummary() {
    if (this.businessParams == null || this.businessParams.trim().isEmpty()) {
        return "无业务参数";
    }
    
    // 如果businessParams长度过长，截取前100个字符并添加省略号
    String params = this.businessParams.trim();
    if (params.length() > 100) {
        return params.substring(0, 100) + "...";
    }
    
    return params;
}
```

#### 3.3 新增辅助方法

```java
// 通用参数获取方法（支持动态参数）
public String getBusinessParamValue(String paramName)

// 获取所有业务参数的Map形式
public Map<String, Object> getBusinessParamsMap()

// 设置业务参数（从Map转换为JSON格式，支持动态参数）
public void setBusinessParamsFromMap(Map<String, Object> paramsMap)

// 设置业务参数（兼容原有方法）
public void setBusinessParamsFromValues(String outReason, String auditDesc, String adjustReason)
```

### 4. Controller层优化

#### 4.1 动态参数收集

新增`collectBusinessParams`方法，自动收集前端传递的所有业务参数：

```java
private Map<String, Object> collectBusinessParams(HttpServletRequest request, String outReason, String auditDesc, String adjustReason) {
    Map<String, Object> businessParams = new HashMap<>();

    // 添加固定参数（向下兼容）
    if (StringUtils.isNotBlank(outReason)) {
        businessParams.put("outReason", outReason);
    }
    // ... 其他固定参数

    // 收集动态参数（以"businessParam_"开头）
    Enumeration<String> paramNames = request.getParameterNames();
    while (paramNames.hasMoreElements()) {
        String paramName = paramNames.nextElement();
        if (paramName.startsWith("businessParam_")) {
            String actualParamName = paramName.substring("businessParam_".length());
            String paramValue = request.getParameter(paramName);
            if (StringUtils.isNotBlank(actualParamName) && StringUtils.isNotBlank(paramValue)) {
                businessParams.put(actualParamName, paramValue);
            }
        }
    }

    return businessParams;
}
```

#### 4.2 参数验证优化

支持动态参数的验证，根据参数名称设置不同的长度限制：

```java
private void validateBusinessParams(Map<String, Object> businessParams) {
    for (Map.Entry<String, Object> entry : businessParams.entrySet()) {
        String paramName = entry.getKey();
        Object paramValue = entry.getValue();

        if (paramValue != null) {
            String valueStr = paramValue.toString();
            int maxLength = getMaxLengthForParam(paramName);
            if (valueStr.length() > maxLength) {
                throw new IllegalArgumentException("业务参数[" + paramName + "]长度不能超过" + maxLength + "个字符");
            }
        }
    }
}
```

### 5. Service层优化

#### 3.1 参数恢复逻辑优化

**优化前**：
```java
String finalOutReason = StringUtils.isNotBlank(outReason) ? outReason : latestHistory.getOutReason();
String finalAuditDesc = StringUtils.isNotBlank(auditDesc) ? auditDesc : latestHistory.getAuditDesc();
String finalAdjustReason = StringUtils.isNotBlank(adjustReason) ? adjustReason : latestHistory.getAdjustReason();
```

**优化后**：
```java
String finalOutReason = StringUtils.isNotBlank(outReason) ? outReason : latestHistory.getOutReasonFromBusinessParams();
String finalAuditDesc = StringUtils.isNotBlank(auditDesc) ? auditDesc : latestHistory.getAuditDescFromBusinessParams();
String finalAdjustReason = StringUtils.isNotBlank(adjustReason) ? adjustReason : latestHistory.getAdjustReasonFromBusinessParams();
```

#### 3.2 参数保存逻辑优化

**优化前**：
```java
history.setOutReason(outReason);
history.setAuditDesc(auditDesc);
history.setAdjustReason(adjustReason);
```

**优化后**：
```java
history.setBusinessParamsFromValues(outReason, auditDesc, adjustReason);
```

### 4. Controller层优化

#### 4.1 接口定义补充

在`IMsgsSignaturehistoryService`接口中添加了支持业务参数的`batchSignature`方法定义：

```java
List<String> batchSignature(UserVO userVO, String familyInfoIds, String templateId, String fileType, 
                          Date stampDate, String keyword, String outReason, String auditDesc, String adjustReason) throws IOException;
```

#### 4.2 调试接口优化

`getBusinessParams`接口中的参数获取方式优化：

**优化前**：
```java
detail.put("outReason", signature.getOutReason());
detail.put("auditDesc", signature.getAuditDesc());
detail.put("adjustReason", signature.getAdjustReason());
```

**优化后**：
```java
detail.put("outReason", signature.getOutReasonFromBusinessParams());
detail.put("auditDesc", signature.getAuditDescFromBusinessParams());
detail.put("adjustReason", signature.getAdjustReasonFromBusinessParams());
```

## 优化优势

### 1. 数据一致性
- 所有业务参数统一存储在一个字段中，避免数据不一致
- 减少数据冗余，简化数据维护

### 2. 扩展性
- 新增业务参数只需要修改JSON结构，无需修改数据库表结构
- 支持动态参数，便于未来功能扩展

### 3. 维护性
- 代码逻辑更清晰，减少重复代码
- 统一的参数处理方式，降低维护成本

### 4. 性能优化
- 减少数据库字段数量，提高查询性能
- JSON解析性能良好，适合业务参数场景

## 向下兼容

优化保持了向下兼容性：
- 原有的`getOutReason()`、`getAuditDesc()`、`getAdjustReason()`方法仍然可用
- 新增的方法提供了从`businessParams`中解析参数的能力
- 现有业务逻辑无需大幅修改

## 测试验证

创建了完整的单元测试类`MsgsSignaturehistoryBusinessParamsTest`，覆盖以下场景：
- 设置完整业务参数
- 设置部分业务参数
- 设置空业务参数
- 长文本摘要处理
- JSON特殊字符转义
- 完整工作流程测试

## 使用示例

### 设置业务参数
```java
MsgsSignaturehistory history = new MsgsSignaturehistory();
history.setBusinessParamsFromValues("户籍迁移", "经审核符合条件", "金额调整");
```

### 获取业务参数
```java
String outReason = history.getOutReasonFromBusinessParams();
String auditDesc = history.getAuditDescFromBusinessParams();
String adjustReason = history.getAdjustReasonFromBusinessParams();
```

### 检查是否有业务参数
```java
if (history.hasBusinessParams()) {
    String summary = history.getBusinessParamsSummary();
    // 处理业务参数
}
```

## 注意事项

1. **JSON格式**：businessParams字段存储的是标准JSON格式，确保数据的结构化和可解析性
2. **特殊字符处理**：自动处理JSON中的特殊字符转义，确保数据安全
3. **长度限制**：摘要功能自动截取长文本，避免显示问题
4. **空值处理**：正确处理null和空字符串，确保逻辑健壮性

## 迁移建议

如果需要将现有数据迁移到新的存储方式：

1. **数据迁移脚本**：将现有的`outReason`、`auditDesc`、`adjustReason`字段数据合并到`businessParams`字段
2. **渐进式迁移**：保持新旧方式并存，逐步迁移到新方式
3. **测试验证**：充分测试确保数据迁移的正确性和完整性

优化后的业务参数管理更加统一、高效和易维护，为后续功能扩展奠定了良好基础。
