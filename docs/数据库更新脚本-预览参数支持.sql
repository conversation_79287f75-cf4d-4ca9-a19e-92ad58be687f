-- 电子签章动态参数丢失问题修复 - 数据库更新脚本
-- 添加预览参数支持字段，解决签章时动态参数丢失的问题
-- 执行时间：2025-09-25
-- 作者：系统

-- 1. 为 msgs_signaturehistory 表添加预览参数相关字段
ALTER TABLE msgs_signaturehistory ADD COLUMN preview_params CLOB;
COMMENT ON COLUMN msgs_signaturehistory.preview_params IS '预览时的动态参数（JSON格式存储）';

ALTER TABLE msgs_signaturehistory ADD COLUMN is_preview_record NUMBER(1) DEFAULT 0;
COMMENT ON COLUMN msgs_signaturehistory.is_preview_record IS '是否为预览记录（0：正式签章记录，1：预览记录）';

-- 2. 创建索引以提高查询性能
CREATE INDEX idx_msgs_signaturehistory_preview ON msgs_signaturehistory(family_id, document_type, is_preview_record, delete_mark);
COMMENT ON INDEX idx_msgs_signaturehistory_preview IS '预览记录查询索引';

-- 3. 更新现有记录，将所有现有记录标记为正式签章记录
UPDATE msgs_signaturehistory SET is_preview_record = 0 WHERE is_preview_record IS NULL;

-- 4. 添加约束确保字段值的有效性
ALTER TABLE msgs_signaturehistory ADD CONSTRAINT chk_is_preview_record CHECK (is_preview_record IN (0, 1));

-- 5. 创建视图，方便查询预览记录和正式签章记录
CREATE OR REPLACE VIEW v_msgs_signature_preview AS
SELECT 
    family_id,
    document_type,
    preview_params,
    create_date,
    create_user_name,
    ROW_NUMBER() OVER (PARTITION BY family_id, document_type ORDER BY create_date DESC) as rn
FROM msgs_signaturehistory 
WHERE is_preview_record = 1 
  AND delete_mark = 0
  AND preview_params IS NOT NULL;

COMMENT ON VIEW v_msgs_signature_preview IS '最新预览参数视图';

-- 6. 创建视图，方便查询正式签章记录
CREATE OR REPLACE VIEW v_msgs_signature_formal AS
SELECT 
    family_id,
    document_type,
    business_params,
    signature_level,
    signature_keyword,
    create_date,
    create_user_name,
    ROW_NUMBER() OVER (PARTITION BY family_id, document_type ORDER BY create_date DESC) as rn
FROM msgs_signaturehistory 
WHERE is_preview_record = 0 
  AND delete_mark = 0;

COMMENT ON VIEW v_msgs_signature_formal IS '正式签章记录视图';

-- 7. 验证脚本执行结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_preview_record = 0 THEN 1 END) as formal_records,
    COUNT(CASE WHEN is_preview_record = 1 THEN 1 END) as preview_records,
    COUNT(CASE WHEN preview_params IS NOT NULL THEN 1 END) as records_with_preview_params
FROM msgs_signaturehistory 
WHERE delete_mark = 0;

-- 输出执行结果说明
SELECT '数据库更新完成！' as status,
       '已添加预览参数支持字段' as description,
       '现在可以在预览时保存动态参数，签章时自动恢复' as feature
FROM dual;
