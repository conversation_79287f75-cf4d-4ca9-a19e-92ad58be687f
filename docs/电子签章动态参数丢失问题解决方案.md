# 电子签章动态参数丢失问题解决方案

## 问题描述

在电子签章系统中，当用户第一次生成Word文书模板时，前端传来的动态参数（如停保原因`outReason`、审核描述`auditDesc`、调整原因`adjustReason`等）会正确绑定到Word中。但是，当用户点击电子签章时，这些动态参数会丢失，导致生成的PDF文件中缺少重要信息。

### 动态参数包括但不限于：
- `outReason` - 停保原因
- `auditDesc` - 审核描述
- `adjustReason` - 调整原因
- `stampDate` - 签章日期
- `publicStartDate` - 公示开始日期
- `publicEndDate` - 公示结束日期
- 以及任何前端传递的自定义参数

## 问题根本原因

1. **预览阶段参数未保存**：在生成预览时，动态参数只用于生成PDF预览，但没有保存到数据库中
2. **签章阶段参数传递断层**：在电子签章时，如果前端没有再次传递动态参数，系统无法获取到这些参数
3. **参数恢复机制不完善**：缺少从预览记录中恢复参数的机制

## 解决方案概述

本解决方案通过以下几个关键改进来解决动态参数丢失问题：

1. **增强预览接口参数保存**：在生成预览时将动态参数保存到数据库
2. **优化签章接口参数恢复**：在签章时从历史记录中恢复参数
3. **实现智能参数变更检测**：当参数变化时重新生成PDF，未变化时复用文件
4. **添加完善的参数验证和日志**：便于问题排查和调试

## 技术实现详情

### 1. 数据库结构增强

在`msgs_signaturehistory`表中添加了两个新字段：

```sql
-- 预览时的动态参数（JSON格式存储）
ALTER TABLE msgs_signaturehistory ADD COLUMN preview_params CLOB;

-- 是否为预览记录（0：正式签章记录，1：预览记录）
ALTER TABLE msgs_signaturehistory ADD COLUMN is_preview_record NUMBER(1) DEFAULT 0;
```

### 2. 实体类增强

在`MsgsSignaturehistory`实体类中添加了预览参数处理方法：

- `setPreviewParamsFromMap()` - 设置预览参数
- `getPreviewParamsMap()` - 获取预览参数
- `hasPreviewParams()` - 检查是否有预览参数
- `isPreviewRecord()` - 检查是否为预览记录

### 3. 预览接口增强

修改了预览生成方法，在生成预览时自动保存动态参数。**重要**：传统的业务参数（`outReason`、`auditDesc`、`adjustReason`）也被统一转换为动态参数处理：

```java
// 传统业务参数转换为动态参数
Map<String, Object> dynamicParams = new HashMap<>();
if (StringUtils.isNotBlank(outReason)) {
    dynamicParams.put("outReason", outReason);
}
if (StringUtils.isNotBlank(auditDesc)) {
    dynamicParams.put("auditDesc", auditDesc);
}
if (StringUtils.isNotBlank(adjustReason)) {
    dynamicParams.put("adjustReason", adjustReason);
}

// 保存预览参数到数据库
if (dynamicParams != null && !dynamicParams.isEmpty()) {
    savePreviewParams(familyId, templateId, fileType, dynamicParams);
}
```

### 4. 签章接口增强

修改了签章处理方法，在没有传递参数时从预览记录中恢复。**重要**：传统的业务参数接口也被统一转换为动态参数处理：

```java
// 传统业务参数转换为动态参数
Map<String, Object> businessParams = new HashMap<>();
if (StringUtils.isNotBlank(outReason)) {
    businessParams.put("outReason", outReason);
}
if (StringUtils.isNotBlank(auditDesc)) {
    businessParams.put("auditDesc", auditDesc);
}
if (StringUtils.isNotBlank(adjustReason)) {
    businessParams.put("adjustReason", adjustReason);
}

// 如果当前没有传递业务参数，尝试从预览记录中恢复
if (finalBusinessParams.isEmpty()) {
    Map<String, Object> previewParams = getPreviewParams(familyInfoId, fileType);
    if (!previewParams.isEmpty()) {
        finalBusinessParams.putAll(previewParams);
        log.info("从预览记录恢复动态参数，家庭ID：{}, 参数：{}", familyInfoId, previewParams);
    }
}
```

### 5. 参数验证工具

创建了`DynamicParamsValidator`工具类，提供：

- 参数有效性验证
- 参数变更检测
- 详细的日志记录
- 特殊字符处理

### 6. 智能参数变更检测

实现了智能的参数变更检测机制：

```java
private boolean shouldRegeneratePdfForDynamicBusinessParams(MsgsSignaturehistory latestHistory,
                                                          Map<String, Object> currentBusinessParams) {
    // 使用参数验证工具进行详细比较
    DynamicParamsValidator.ComparisonResult comparison = 
            DynamicParamsValidator.compareParams(historyParams, currentBusinessParams, context);
    
    return comparison.hasChanges();
}
```

## 使用方式

### 1. 前端调用预览接口

```javascript
// 使用JSON格式传递动态参数
POST /msgs_signaturehistory/viewWithJson
{
  "familyInfoIds": "1001,1002",
  "templateId": "dibao_approval", 
  "fileType": "pdf",
  "stampDate": "2025-09-25",
  "extendParams": {
    "outReason": "户籍迁移",
    "auditDesc": "经审核符合条件",
    "adjustReason": "金额调整",
    "customParam1": "自定义参数1"
  }
}
```

### 2. 前端调用签章接口

```javascript
// 签章时可以不传递动态参数，系统会自动从预览记录中恢复
POST /msgs_signaturehistory/batchSignature
{
  "familyInfoIds": "1001,1002",
  "templateId": "dibao_approval",
  "fileType": "1",
  "keyword": "县级审批"
  // 不需要再次传递动态参数，系统会自动恢复
}
```

## 关键特性

### 1. 自动参数恢复
- 预览时自动保存动态参数
- 签章时自动恢复参数
- 支持多级签章的参数传递

### 2. 智能PDF生成
- 参数变化时重新生成PDF
- 参数未变化时复用已有文件
- 提高系统性能

### 3. 完善的参数验证
- 参数格式验证
- 参数长度检查
- 特殊字符处理
- 详细的错误提示

### 4. 详细的日志记录
- 参数传递过程日志
- 参数变更检测日志
- 错误和警告日志
- 便于问题排查

## 兼容性说明

### 1. 向后兼容
- 保持原有接口不变
- 支持原有的参数传递方式
- 不影响现有功能

### 2. 数据库兼容
- 新增字段有默认值
- 现有数据不受影响
- 支持渐进式升级

## 部署步骤

### 1. 执行数据库更新脚本
```sql
-- 执行 docs/数据库更新脚本-预览参数支持.sql
```

### 2. 部署新版本代码
- 包含所有增强的服务类
- 包含新的验证工具类
- 包含更新的实体类

### 3. 验证功能
- 运行测试用例
- 验证预览功能
- 验证签章功能
- 检查日志输出

## 测试验证

### 1. 单元测试
运行`DynamicParamsRecoveryTest`测试类，验证：
- 参数验证功能
- 参数比较功能
- 参数序列化功能
- 边界情况处理

### 2. 集成测试
1. 生成预览并传递动态参数
2. 检查数据库中是否保存了预览参数
3. 进行签章操作（不传递参数）
4. 验证生成的PDF中包含动态参数

### 3. 性能测试
- 验证参数保存不影响预览性能
- 验证参数恢复不影响签章性能
- 验证智能PDF生成提高整体性能

## 监控和维护

### 1. 日志监控
关注以下关键日志：
- `从预览记录恢复动态参数`
- `检测到业务参数变化，重新生成PDF文件`
- `动态参数验证失败`

### 2. 数据库监控
- 监控预览记录的增长
- 定期清理过期的预览记录
- 监控参数存储空间使用

### 3. 性能监控
- 监控预览生成时间
- 监控签章处理时间
- 监控PDF生成频率

## 快速验证步骤

### 验证脚本
```bash
# 1. 生成预览并传递动态参数
curl -X POST "http://localhost:8080/msgs_signaturehistory/viewWithJson" \
  -H "Content-Type: application/json" \
  -d '{
    "familyInfoIds": "test_family_001",
    "templateId": "dibao_approval",
    "fileType": "pdf",
    "extendParams": {
      "outReason": "户籍迁移测试",
      "auditDesc": "经审核符合条件测试",
      "customParam": "自定义参数测试"
    }
  }'

# 2. 检查预览参数是否保存
curl -X GET "http://localhost:8080/msgs_signaturehistory/getBusinessParams?familyId=test_family_001&documentType=1"

# 3. 进行签章（不传递动态参数）
curl -X POST "http://localhost:8080/msgs_signaturehistory/batchSignature" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d 'familyInfoIds=test_family_001&templateId=dibao_approval&fileType=1&keyword=县级审批'
```

### 验证要点
1. **预览阶段**：检查日志中是否有"保存新的预览参数记录"
2. **签章阶段**：检查日志中是否有"从预览记录恢复动态参数"
3. **PDF内容**：验证生成的PDF中包含动态参数内容

## 总结

本解决方案彻底解决了电子签章过程中动态参数丢失的问题，通过预览时保存参数、签章时自动恢复的机制，确保了参数的连续性和完整性。同时，通过智能的参数变更检测和完善的验证机制，提高了系统的稳定性和可维护性。

该方案具有以下优势：
- ✅ **彻底解决参数丢失问题**
- ✅ **保持向后兼容性**
- ✅ **提供完善的参数验证**
- ✅ **支持动态参数扩展**
- ✅ **提供详细的日志记录**
- ✅ **提高系统性能**
