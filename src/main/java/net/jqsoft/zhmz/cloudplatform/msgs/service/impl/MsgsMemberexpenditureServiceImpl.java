package net.jqsoft.zhmz.cloudplatform.msgs.service.impl;

import net.jqsoft.common.lang.Converter;
import net.jqsoft.persist.mybatisplus.query.UpdateWrapper;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.remoteservice.RemoteBasicService;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsMemberexpenditure;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.StringUtils;
import net.jqsoft.zhmz.common.vo.DictVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.jqsoft.persist.mybatisplus.support.MySuperServiceImpl;
import net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsMemberexpenditureMapper;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IMsgsMemberexpenditureService;
import net.jqsoft.persist.mybatisplus.query.Page;
import net.jqsoft.apis.util.QueryParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description 
 * <AUTHOR>
 * @date 2023-11-23 17:25:29
 */
@Service
public class MsgsMemberexpenditureServiceImpl extends MySuperServiceImpl<MsgsMemberexpenditure, MsgsMemberexpenditureMapper> implements IMsgsMemberexpenditureService {

    @Autowired
    RemoteBasicService remoteBasicService;

    @Override
    public List<MsgsMemberexpenditure> selectMsgsMemberexpenditureList(QueryParams conditions) {
		return this.querier()
                .eq("DELETE_MARK",0)
                .eq( StringUtils.isNotEmpty(conditions.getString("memberid")),"MEMBER_ID", conditions.getString("memberid"))
				.list();
    }

    @Override
    public Page<MsgsMemberexpenditure> selectMsgsMemberexpenditurePage(QueryParams conditions) {
		return this.querier()
                .eq("DELETE_MARK",0)
                .eq( StringUtils.isNotEmpty(conditions.getString("memberid")),"MEMBER_ID", conditions.getString("memberid"))
				.setCurrentPage(conditions.getPageNum())
				.setPageSize(conditions.getPageSize())
				.page();
    }

    @Override
    public List<MsgsMemberexpenditure> selectByMemberIds(List<String> memberIds) {
        return this.querier()
                .eq("DELETE_MARK",0)
                .in( "MEMBER_ID", memberIds)
                .list();
    }

    @Override
    public boolean deleteByMemberid(String memberId) {

        return this.update(new UpdateWrapper<MsgsMemberexpenditure>()
                .eq("MEMBER_ID",memberId)
                .set("DELETE_MARK",1)
        );

    }

    @Override
    public List<MsgsMemberexpenditure> selectExpenditureListByMembId(List<String> membIds) {
        return this.baseMapper.selectExpenditureListByMembId(membIds);
    }

    /**
     * 根据字典构建列表数据
     */
    @Override
    public List<MsgsMemberexpenditure> buildListByDict(Map<String, Object> queryParams) {
        String memberid = Converter.toString(queryParams.get("memberid"));
        if (StringUtils.isBlank(memberid)) {
            return new ArrayList<>();
        }
        // 查出字典
        List<DictVO> sourceDits = remoteBasicService.selectChildrenByParentCode("expend_type", "zhmz-cloudplatform-msgs").getData();
        List<MsgsMemberexpenditure> list = new ArrayList<>();
        for (DictVO dict : sourceDits) {
            MsgsMemberexpenditure entity = new MsgsMemberexpenditure();
            entity.setMemberId(memberid);
            entity.setHouseholdExpenditureVal(Converter.toInteger(dict.getContent()));
            entity.setHouseholdExpenditureKey(dict.getName());
            list.add(entity);
        }
        return list;
    }
}