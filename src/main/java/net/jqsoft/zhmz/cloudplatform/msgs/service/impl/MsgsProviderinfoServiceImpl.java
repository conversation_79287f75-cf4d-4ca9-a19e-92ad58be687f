package net.jqsoft.zhmz.cloudplatform.msgs.service.impl;

import net.jqsoft.persist.mybatisplus.query.UpdateWrapper;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsProviderinfo;
import org.springframework.stereotype.Service;
import net.jqsoft.persist.mybatisplus.support.MySuperServiceImpl;
import net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsProviderinfoMapper;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IMsgsProviderinfoService;
import net.jqsoft.persist.mybatisplus.query.Page;
import net.jqsoft.apis.util.QueryParams;
import java.util.List;

/**
 * @description 
 * <AUTHOR>
 * @date 2023-11-08 16:40:50
 */
@Service
public class MsgsProviderinfoServiceImpl extends MySuperServiceImpl<MsgsProviderinfo, MsgsProviderinfoMapper> implements IMsgsProviderinfoService {

    @Override
    public List<MsgsProviderinfo> selectMsgsProviderinfoList(QueryParams conditions) {
		return this.querier()
				.list();
    }

    @Override
    public Page<MsgsProviderinfo> selectMsgsProviderinfoPage(QueryParams conditions) {
		return this.querier()
				.setCurrentPage(conditions.getPageNum())
				.setPageSize(conditions.getPageSize())
				.page();
    }

    @Override
    public List<MsgsProviderinfo> selectByFamilyid(String familyId) {
        return this.querier()
                .eq("DELETE_MARK",0)
                .eq("FAMILY_ID", familyId)
                .list();
    }
    @Override
    public boolean deleteByFamilyid(String familyId) {
        return this.update(new UpdateWrapper<MsgsProviderinfo>()
                .eq("FAMILY_ID",familyId)
                .set("DELETE_MARK",1)
        );
    }
}