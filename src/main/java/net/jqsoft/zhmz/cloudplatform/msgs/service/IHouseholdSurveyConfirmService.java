package net.jqsoft.zhmz.cloudplatform.msgs.service;

import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyConfirm;
import net.jqsoft.zhmz.cloudplatform.msgs.model.dto.householdsurvey.HouseholdSurveyDto;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.crud.ICrudService;

/**
 * <AUTHOR>
 * @description
 * @date 2025-05-19 17:35:13
 */
public interface IHouseholdSurveyConfirmService extends ICrudService<HouseholdSurveyConfirm> {

    HouseholdSurveyConfirm selectByhsId(String hsId);

    void start(boolean isSync, HouseholdSurveyDto survey);

    void push(boolean isPush, HouseholdSurveyDto survey);
}