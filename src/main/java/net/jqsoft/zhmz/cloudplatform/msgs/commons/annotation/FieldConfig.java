package net.jqsoft.zhmz.cloudplatform.msgs.commons.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({})
public @interface FieldConfig {

    /**
     * 要校验的字段名
     */
    String field();

    /**
     * 校验失败提示信息
     */
    String message();
}
