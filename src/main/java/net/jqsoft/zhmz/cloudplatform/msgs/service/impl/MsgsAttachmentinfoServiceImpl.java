package net.jqsoft.zhmz.cloudplatform.msgs.service.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import net.jqsoft.apis.util.QueryParams;
import net.jqsoft.common.domain.JsonResult;
import net.jqsoft.common.lang.Converter;
import net.jqsoft.persist.mybatisplus.query.Page;
import net.jqsoft.persist.mybatisplus.query.UpdateWrapper;
import net.jqsoft.persist.mybatisplus.support.MySuperServiceImpl;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.FileCategory;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.FileMimeType;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.remoteservice.RemoteOssService;
import net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsAttachmentinfoMapper;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsAttachmentinfo;
import net.jqsoft.zhmz.cloudplatform.msgs.model.vo.UploadVo;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IMsgsAttachmentinfoService;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.StringUtils;
import net.jqsoft.zhmz.common.vo.UserVO;
import org.jsets.fastboot.security.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023-11-22 16:03:28
 */
@Slf4j
@Service
public class MsgsAttachmentinfoServiceImpl extends MySuperServiceImpl<MsgsAttachmentinfo, MsgsAttachmentinfoMapper> implements IMsgsAttachmentinfoService {

    @Autowired
    private RemoteOssService remoteOssService;

    @Autowired
    private IMsgsAttachmentinfoService msgsAttachmentinfoService;

    @Override
    public List<MsgsAttachmentinfo> selectAttachmentInfoList(QueryParams conditions) {
        String objectId = Converter.toString(conditions.get("objectId"));
        String memberId = Converter.toString(conditions.get("memberId"));
        Integer objectType = Converter.toInteger(conditions.get("objectType"));
        return this.querier()
                .eq("DELETE_MARK", 0)
                .eq(StringUtils.isNotEmpty(objectId), "OBJECT_ID", objectId)
                .eq(StringUtils.isNotEmpty(memberId), "MEMBER_ID", memberId)
                .eq(objectType != null, "OBJECT_TYPE", objectType)
                .list();
    }

    @Override
    public Page<MsgsAttachmentinfo> selectAttachmentInfoPage(QueryParams conditions) {
        String objectId = Converter.toString(conditions.get("objectId"));
        Integer objectType = Converter.toInteger(conditions.get("objectType"));
        return this.querier()
                .setCurrentPage(conditions.getPageNum())
                .setPageSize(conditions.getPageSize())
                .eq("DELETE_MARK", 0)
                .eq(StringUtils.isNotEmpty(objectId), "OBJECT_ID", objectId)
                .eq(objectType != null, "OBJECT_TYPE", objectType)
                .page();
    }

    @Override
    public void saveData(MsgsAttachmentinfo entity) {
        UserVO userVO = SecurityUtils.getAccount();
        entity.setDeleteMark(0);
        entity.setCreateDate(new Date());
        entity.setCreateUserId(userVO.getId());
        entity.setCreateUserName(userVO.getRealName());
        this.save(entity);
    }

    @Override
    public void saveList(List<MsgsAttachmentinfo> list) {
        UserVO userVO = SecurityUtils.getAccount();
        list.stream().forEach(entity -> {
            entity.setDeleteMark(0);
            entity.setCreateDate(new Date());
            entity.setCreateUserId(userVO.getId());
            entity.setCreateUserName(userVO.getRealName());
        });
        this.saveBatch(list);
    }

    @Override
    public void deleteDataById(String id) {
        UserVO userVO = SecurityUtils.getAccount();
        this.update(new UpdateWrapper<MsgsAttachmentinfo>()
                .eq("ID", id)
                .set("DELETE_MARK", 1)
                .set("MODIFY_DATE", new Date())
                .set("MODIFY_USER_ID", userVO.getId())
                .set("MODIFY_USER_NAME", userVO.getRealName())
        );
    }

    @Override
    public void updateData(MsgsAttachmentinfo entity) {
        UserVO userVO = SecurityUtils.getAccount();
        entity.setModifyDate(new Date());
        entity.setModifyUserId(userVO.getId());
        entity.setModifyUserName(userVO.getRealName());
        this.save(entity);
    }

    @Override
    public void deleteDataByIds(String[] ids) {
        UserVO userVO = SecurityUtils.getAccount();
        this.update(new UpdateWrapper<MsgsAttachmentinfo>()
                .in("ID", ids)
                .set("DELETE_MARK", 1)
                .set("MODIFY_DATE", new Date())
                .set("MODIFY_USER_ID", userVO.getId())
                .set("MODIFY_USER_NAME", userVO.getRealName())
        );
    }

    @Override
    public List<MsgsAttachmentinfo> getAllByFamilyId(String familyId) {
        return this.querier()
                .eq("OBJECT_ID", familyId)
                .list();
    }

    @Override
    public void syncByObjectId(String sourceObjId, String targetObjId) {
        
        // 查询源附件和目标附件
        List<MsgsAttachmentinfo> sourceAttachments = this.selectList(
                this.buildLambdaQuery()
                        .eq(MsgsAttachmentinfo::getDeleteMark, 0)
                        .eq(MsgsAttachmentinfo::getObjectId, sourceObjId)
        );

        List<MsgsAttachmentinfo> targetAttachments = this.selectList(
                this.buildLambdaQuery()
                        .eq(MsgsAttachmentinfo::getDeleteMark, 0)
                        .eq(MsgsAttachmentinfo::getObjectId, targetObjId)
        );

        // 若两者均为空，提前返回
        if (CollectionUtils.isEmpty(sourceAttachments) && CollectionUtils.isEmpty(targetAttachments)) {
            return;
        }

        // 构建 filePath 映射表（若存在重复路径，请检查源数据）
        Map<String, MsgsAttachmentinfo> sourceMap = sourceAttachments.stream()
                .collect(Collectors.toMap(MsgsAttachmentinfo::getFilePath, Function.identity(), (a, b) -> a));

        Map<String, MsgsAttachmentinfo> targetMap = targetAttachments.stream()
                .collect(Collectors.toMap(MsgsAttachmentinfo::getFilePath, Function.identity(), (a, b) -> a));

        // 1. 找出源有但目标没有的 → 需要新增
        List<MsgsAttachmentinfo> toAdd = sourceAttachments.stream()
                .filter(source -> !targetMap.containsKey(source.getFilePath()))
                .map(source -> {
                    MsgsAttachmentinfo copy = new MsgsAttachmentinfo();
                    BeanUtils.copyProperties(source, copy);
                    copy.setId(null); // 清空 ID 表示新增
                    copy.setObjectId(targetObjId);
                    return copy;
                })
                .collect(Collectors.toList());

        // 2. 找出目标有但源没有的 → 需要删除
        List<String> toDeleteIds = targetAttachments.stream()
                .filter(target -> !sourceMap.containsKey(target.getFilePath()))
                .map(MsgsAttachmentinfo::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 执行新增操作
        if (!toAdd.isEmpty()) {
            this.saveBatch(toAdd);
        }

        // 执行删除操作
        if (!toDeleteIds.isEmpty()) {
            this.deleteDataByIds(toDeleteIds.toArray(new String[0]));
        }
    }

    /**
     * 保存附件
     *
     * @param pdfStream
     * @param id
     * @param fileName
     */
    @Override
    public void saveAttachment(ByteArrayOutputStream pdfStream, String id, String fileName) {
        try {
            UploadVo uploadVo = new UploadVo();
            uploadVo.setAppName("zhmz.cloudplatform.msgs");
            uploadVo.setFileBase64(Base64.getEncoder().encodeToString(pdfStream.toByteArray()));
            uploadVo.setRemark("上传模板");
            uploadVo.setFileName(fileName);
            uploadVo.setBizType(FileMimeType.PDF.getExtension());

            JsonResult ossUploadResult = remoteOssService.uploadBase64(uploadVo);
            if (!ossUploadResult.isSucceed()) {
                log.error("OSS上传失败，响应内容: {}", ossUploadResult.getMessage());
                throw new RuntimeException("文件上传失败，请重试");
            }
            String fileId = ossUploadResult.get("data").toString();
            if (fileId.isEmpty()) {
                log.error("OSS返回文件ID为空");
                throw new RuntimeException("文件上传失败，未获取到文件ID");
            }

            UserVO userVO = SecurityUtils.getAccount();
            MsgsAttachmentinfo attachment = new MsgsAttachmentinfo();
            attachment.setId(UUID.fastUUID().toString());
            attachment.setObjectType(0);
            attachment.setFileCategory(FileCategory.OTHER.getCode());
            attachment.setObjectId(id);
            attachment.setFileName(fileName);
            attachment.setFilePath(fileId);
            attachment.setFileSize(String.valueOf(pdfStream.size()));
            attachment.setFileExtensions(FileMimeType.PDF.getExtension());
            attachment.setFileType(FileMimeType.PDF.getMimeType());
            attachment.setDownloadCount(0);
            attachment.setIsTop(0);
            attachment.setEnabledMark(0);
            attachment.setDeleteMark(0);
            attachment.setCreateDate(new Date());
            attachment.setCreateUserId(userVO.getId());
            attachment.setCreateUserName(userVO.getRealName());
            attachment.setModifyDate(new Date());
            attachment.setModifyUserId(userVO.getId());
            attachment.setModifyUserName(userVO.getRealName());
            attachment.setFileCategoryName(FileCategory.OTHER.getMsg());

            boolean success = msgsAttachmentinfoService.save(attachment);
            if (!success) {
                log.error("附件信息保存失败，家庭ID: {}", id);
                throw new RuntimeException("附件信息保存失败");
            }

            log.info("附件上传成功，家庭ID: {}, 文件ID: {}", id, fileId);

        } catch (Exception e) {
            log.error("保存附件信息失败，家庭ID: {}, 类型: {}", id, fileName, e);
            throw new RuntimeException("附件保存失败，请检查系统状态", e);
        }
    }
}