package net.jqsoft.zhmz.cloudplatform.msgs.mapper;

import net.jqsoft.apis.util.QueryParams;
import net.jqsoft.persist.mybatisplus.mapper.SuperMapper;
import net.jqsoft.persist.mybatisplus.query.Page;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsSigninDetail;
import net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsSigninDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024-06-11 17:11:31
 */
@Mapper
public interface MsgsSigninDetailMapper extends SuperMapper<MsgsSigninDetail> {

    List<MsgsSigninDetailVO> getSignDetailList(@Param("params") QueryParams params);

    Page<MsgsSigninDetailVO> getSignDetailList(Page<Map<String, Object>> page, @Param("params") QueryParams params);

    String getSignDetailId(@Param("params") QueryParams params);
}