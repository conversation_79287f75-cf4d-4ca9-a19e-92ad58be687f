package net.jqsoft.zhmz.cloudplatform.msgs.controller;

import com.sun.javafx.binding.StringFormatter;
import lombok.extern.slf4j.Slf4j;
import net.jqsoft.common.domain.*;
import net.jqsoft.common.lang.Converter;
import net.jqsoft.excel.innter.ExcelMate;
import net.jqsoft.excel.service.ExcelService;
import net.jqsoft.persist.mybatisplus.query.QueryWrapper;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsDeathpeople;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsDeccaDetail;
import net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsDeccaDetailVO;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IMsgsDeccaDetailService;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.StringUtils;
import net.jqsoft.zhmz.common.service.remote.RemoteAreaService;
import net.jqsoft.zhmz.common.vo.AreaVO;
import net.jqsoft.zhmz.common.vo.UserVO;
import org.jsets.fastboot.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import net.jqsoft.apis.controller.SuperController;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsDeccaTotal;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IMsgsDeccaTotalService;
import org.springframework.http.MediaType;
import net.jqsoft.apis.util.QueryParams;
import org.springframework.validation.annotation.Validated;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * @description 
 * <AUTHOR>
 * @date 2024-05-15 09:55:33
 */
@Slf4j
@RestController
@RequestMapping("/msgs_decca_total")
public class MsgsDeccaTotalController extends SuperController<String, MsgsDeccaTotal, IMsgsDeccaTotalService> {

	@Autowired
	private ExcelService excelService;
	@Autowired
	private RemoteAreaService remoteAreaService;

	@Autowired
	private IMsgsDeccaDetailService msgsDeccaDetailService;

	/**
	 * 增加
	 */
	@PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public MsgRespond insert(@RequestBody @Validated MsgsDeccaTotal entity) {
		this.service.save(entity);
		return this.successRespond(MSG_INSERT_SUCCEED);
	}

	/**
	 * 删除
	 */
	@DeleteMapping(value="/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public MsgRespond delete(@PathVariable("id") String id) {
		this.service.deleteById(id);
		return this.successRespond(MSG_DELETE_SUCCEED);
	}

	/**
	 * 修改
	 */
	@PutMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public MsgRespond update(@RequestBody @Validated MsgsDeccaTotal entity) {
		this.service.update(entity);
		return this.successRespond(MSG_UPDATE_SUCCEED);
	}

	/**
	 * 根据ID获取
	 */
	@GetMapping(value="/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public DataRespond<MsgsDeccaTotal> get(@PathVariable("id") String id) {
		return this.dataRespond(this.service.selectOne(id));
	}

	/**
	 * 根据参数判断台账和台卡信息
	 */
	@GetMapping(value="/exist", produces = MediaType.APPLICATION_JSON_VALUE)
	public MsgRespond exist(
			@RequestParam(name = "areaCode", required = false) String areaCode
			,@RequestParam(name = "yearMonth", required = false) String yearMonth
			,@RequestParam(name = "familyCategoryVal", required = false) String familyCategoryVal
			,@RequestParam(name = "searType", required = false) Integer searType) {
		QueryParams params = QueryParams.build();
		params.add("areaCode", areaCode);
		params.add("yearMonth", yearMonth);
		params.add("familyCategoryVal", familyCategoryVal);
		params.add("searType", searType);
        String msg=this.service.exist(params);
		return this.successRespond(msg);
	}

	/**
	 * 根据参数判断台账和台卡信息
	 */
	@GetMapping(value="/generate", produces = MediaType.APPLICATION_JSON_VALUE)
	public MsgRespond generate(
			@RequestParam(name = "areaCode", required = false) String areaCode
			,@RequestParam(name = "yearMonth", required = false) String yearMonth
			,@RequestParam(name = "familyCategoryVal", required = false) String familyCategoryVal
			,@RequestParam(name = "searType", required = false) Integer searType) {
		QueryParams params = QueryParams.build();
		params.add("areaCode", areaCode);
		params.add("yearMonth", yearMonth);
		params.add("familyCategoryVal", familyCategoryVal);
		params.add("searType", searType);
		this.service.generate(params);
		return this.successRespond(MSG_INSERT_SUCCEED);
	}
	/**
	 * 根据条件获取列表 
	 * 如果page_num和page_size都不为空进行分页查询否则进行列表查询
	 */
	@GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
	public ListRespond<MsgsDeccaTotal> list(
			@RequestParam(name = "page_num", required = false) Integer pageNum
			,@RequestParam(name = "page_size", required = false) Integer pageSize
			) {
		
		QueryParams params = QueryParams.build();

		if (null != pageNum && null != pageSize) { // 需要分页
			params.setPageNum(pageNum).setPageSize(pageSize);
			return this.pageRespond(this.service.selectMsgsDeccaTotalPage(params));
		}
		// 渲染列表数据的结果
		return this.listRespond(this.service.selectMsgsDeccaTotalList(params));
	}

	/**
	 * 导出
	 */
	@GetMapping("/excel_export")
	public void excelExport(
			@RequestParam(name = "areaCode", required = false) String areaCode
			,@RequestParam(name = "yearMonth", required = false) String yearMonth
			,@RequestParam(name = "familyCategoryVal", required = false) String familyCategoryVal
			,@RequestParam(name = "type", required = true) Integer type
			, HttpServletResponse response
	) {
		QueryParams params = QueryParams.build();
		if (StringUtils.isEmpty(areaCode)){
			UserVO userVO = SecurityUtils.getAccount();
			areaCode = userVO.getAreaId();
		}
		Integer year=Converter.toInteger(yearMonth.substring(0,4));
		Integer month=Converter.toInteger(yearMonth.substring(5));
		params.add("areaCode", areaCode);
		params.add("year", year);
		params.add("month", month);
		params.add("familyCategoryVal", familyCategoryVal);
		params.add("type", type);

		List<MsgsDeccaDetailVO> datas = this.msgsDeccaDetailService.selectMsgsDeccaDetails(params);
		String fileName = "";
		byte[] data = null;
		try {
			if(datas.size()!=0){
				ExcelMate excelMate = excelService.export(datas);
				fileName = excelMate.getFullName();
				data = excelMate.getData();
				response.setHeader("content-type", "application/octet-stream");
				response.setContentType("application/octet-stream");
				response.setHeader("Content-Disposition",
						"attachment;filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));
				OutputStream output = response.getOutputStream();
				output.write(data);
				output.flush();
			}
		} catch (Exception e) {
			log.warn("数据导出失败", e);
		}
	}

	/**
	 * 统计
	 */
	@GetMapping("/statistics")
	public MsgsDeccaTotal statistics(
			@RequestParam(name = "areaCode", required = false) String areaCode
			,@RequestParam(name = "yearMonth", required = false) String yearMonth
			,@RequestParam(name = "familyCategoryVal", required = false) String familyCategoryVal
			,@RequestParam(name = "searType", required = false) Integer searType
	) {
		QueryParams params = QueryParams.build();
		params.add("areaCode", areaCode);
		params.add("yearMonth", yearMonth);
		params.add("familyCategoryVal", familyCategoryVal);
		params.add("searType", searType);
		MsgsDeccaTotal msgsDeccaTotal = this.service.getMsgsDeccaTotal(params);
		return msgsDeccaTotal;
	}

	/**
	 * 根据条件获取乡镇列表
	 *
	 */
	@GetMapping(value="/getTowns/{pId}",produces = MediaType.APPLICATION_JSON_VALUE)
	public ListRespond<AreaVO> getTowns(@PathVariable("pId") String pId
	) {
		List<AreaVO> areaVOS= remoteAreaService.getAreaTree(pId).getData();
		// 渲染列表数据的结果
		return this.listRespond(areaVOS);
	}

	/**
	 * 导出
	 */
	@GetMapping("/txt_export")
	public void txtExport(
			@RequestParam(name = "areaCode", required = false) String areaCode
			,@RequestParam(name = "yearMonth", required = false) String yearMonth
			,@RequestParam(name = "familyCategoryVal", required = false) String familyCategoryVal
			,@RequestParam(name = "type", required = true) Integer type
			, HttpServletResponse response
	) {
		QueryParams params = QueryParams.build();
		if (StringUtils.isEmpty(areaCode)){
			UserVO userVO = SecurityUtils.getAccount();
			areaCode = userVO.getAreaId();
		}
		Integer year=Converter.toInteger(yearMonth.substring(0,4));
		Integer month=Converter.toInteger(yearMonth.substring(5));
		params.add("areaCode", areaCode);
		params.add("year", year);
		params.add("month", month);
		params.add("familyCategoryVal", familyCategoryVal);
		params.add("type", type);

		List<MsgsDeccaDetailVO> datas = this.msgsDeccaDetailService.selectMsgsDeccaDetails(params);

		if(datas.size()>0) {
			String fileName = yearMonth + "信息导出.txt";
//			response.setCharacterEncoding("utf-8");
			//设置响应的内容类型
			response.setContentType("text/plain");
			//设置文件的名称和格式
			BufferedOutputStream buff = null;
			ServletOutputStream outStr = null;
			try {
				response.addHeader("Content-Disposition", "attachment;filename="
						+ new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

				outStr = response.getOutputStream();
				buff = new BufferedOutputStream(outStr);
				String rh = "\t";
				String nh = "\n\n";
				String sFormat = "%-20s";
				String kong=" ";
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				//写入标题
				switch(familyCategoryVal){
					case "01"://农保
						String[] hears01 = {"_QYDM","F1_1","F1_2","F1_3","F1_4","F1_5","F1_6","F1_7","F1_8","F1_9","F1_11","F1_13","F1_16",
						"F1_63","F1_64","F1_14","F1_12","F1_15","F1_61","F1_62","F1_17","F1_18","F1_19","F1_20","F1_21","F1_23", "F1_24",
						"F1_25","F1_26","F1_60","F1_40","F1_41","F1_42","F1_43","F1_44","F1_45","F1_46","F1_47","F1_48",
						"F1_49","F1_50","F1_51","F1_52","F1_53","F1_54","F1_55","F1_56","F1_57","F1_27"};
						for (int i = 0; i < hears01.length; i++) {
							buff.write((hears01[i] + rh).getBytes("UTF-8"));
						}
						buff.write("\n".getBytes("UTF-8"));
						//写入内容F1_1创建日期	F1_2家庭编号	F1_3户主身份证	F1_4户主姓名	F1_5性别 code(1男2女)	F1_6民政（01）	F1_7家庭住址（县-村）	F1_8''	F1_9联系电话	F1_10'' F1_11''   F1_12女性  F1_13老年人  F1_14未成年人  F1_15残疾人  F1_16年轻人 	F1_17发放合计	F1_18分类施保 	F1_19发放金额
						// F1_20 收入 F1_21低保标准 F1_22	F1_23银行名称 F1_24银行账号	F1_25''	F1_26''	F1_60户主是否享受	F1_40姓名1 F1_41  F1_42姓名2	F1_43	F1_44姓名3	F1_45	F1_46姓名4	F1_47	F1_48姓名5	F1_49	F1_50姓名6	F1_51	F1_52姓名7	F1_53	F1_54 姓名8	F1_55  F1_56姓名9	F1_57	F1_27''
						// F1_61重度残疾人  F1_62纳入扶贫建卡对象 F1_63有劳动能力 F1_64无劳动能力  F1_70全自理 F1_71半自理 F1_72全护理
						for (MsgsDeccaDetailVO deccaDetailVO : datas) {
							String memberName1=getValue(deccaDetailVO.getMemberName1());
							String memberName2=getValue(deccaDetailVO.getMemberName2());
							String memberName3=getValue(deccaDetailVO.getMemberName3());
							String memberName4=getValue(deccaDetailVO.getMemberName4());
							String memberName5=getValue(deccaDetailVO.getMemberName5());
							String memberName6=getValue(deccaDetailVO.getMemberName6());
							String memberName7=getValue(deccaDetailVO.getMemberName7());
							String memberName8=getValue(deccaDetailVO.getMemberName8());
							String memberName9=getValue(deccaDetailVO.getMemberName9());
							String memberCard1=getValue(deccaDetailVO.getMemberCard1());
							String memberCard2=getValue(deccaDetailVO.getMemberCard2());
							String memberCard3=getValue(deccaDetailVO.getMemberCard3());
							String memberCard4=getValue(deccaDetailVO.getMemberCard4());
							String memberCard5=getValue(deccaDetailVO.getMemberCard5());
							String memberCard6=getValue(deccaDetailVO.getMemberCard6());
							String memberCard7=getValue(deccaDetailVO.getMemberCard7());
							String memberCard8=getValue(deccaDetailVO.getMemberCard8());
							String memberCard9=getValue(deccaDetailVO.getMemberCard9());
							String sexCode= deccaDetailVO.getSexCode()==1?"2":"1";
							String formattedDate = sdf.format(deccaDetailVO.getApplyDate());
							buff.write((String.format(sFormat, deccaDetailVO.getNumberCode())).getBytes("UTF-8"));//_QYDM台卡区划
							buff.write((String.format(sFormat, formattedDate)).getBytes("UTF-8"));//F1_1创建日期
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyId())).getBytes("UTF-8"));//F1_2家庭编号
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholderIdCard())).getBytes("UTF-8"));//F1_3户主身份证
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholder())).getBytes("UTF-8"));//F1_4户主姓名
							buff.write((String.format(sFormat, sexCode)).getBytes("UTF-8"));//F1_5性别code(1男2女)
							buff.write((String.format(sFormat, deccaDetailVO.getNationalityVal())).getBytes("UTF-8"));//F1_6民族
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyAddress())).getBytes("UTF-8"));//F1_7家庭住址（县-村）
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_8空
							buff.write((String.format(sFormat, deccaDetailVO.getTel())).getBytes("UTF-8"));//F1_9联系电话
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_11''
							buff.write((String.format(sFormat, deccaDetailVO.getOldPeople())).getBytes("UTF-8"));//F1_13老年人
							buff.write((String.format(sFormat, deccaDetailVO.getAdultPeople())).getBytes("UTF-8"));//F1_16年轻人
							//"F1_63","F1_64","F1_14","F1_12","F1_15","F1_61","F1_62","F1_17","F1_18","F1_19","F1_20","F1_21","F1_23", "F1_24",
							buff.write((String.format(sFormat, deccaDetailVO.getHaveLabourCapacity())).getBytes("UTF-8"));//F1_63有劳动能力
							buff.write((String.format(sFormat, deccaDetailVO.getNoLabourCapacity())).getBytes("UTF-8"));//F1_64无劳动能力
							buff.write((String.format(sFormat, deccaDetailVO.getJuveniles())).getBytes("UTF-8"));//F1_14未成年人
							buff.write((String.format(sFormat, deccaDetailVO.getWoman())).getBytes("UTF-8"));//F1_12女性
							buff.write((String.format(sFormat, deccaDetailVO.getDisabled())).getBytes("UTF-8"));//F1_15残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getSeverelyDisabled())).getBytes("UTF-8"));// F1_61重度残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getPoorPerson())).getBytes("UTF-8"));//F1_62纳入扶贫建卡对象
							buff.write((String.format(sFormat, deccaDetailVO.getFinalMoney())).getBytes("UTF-8"));//F1_17发放合计
							buff.write((String.format(sFormat, deccaDetailVO.getClassifyFund())).getBytes("UTF-8"));//F1_18分类施保
							buff.write((String.format(sFormat, deccaDetailVO.getReliefFund())).getBytes("UTF-8"));//F1_19救助金
							buff.write((String.format(sFormat, deccaDetailVO.getMonthIncome())).getBytes("UTF-8"));//F1_20收入
							buff.write((String.format(sFormat, deccaDetailVO.getSubsidyStandard())).getBytes("UTF-8"));//F1_21低保标准
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankVal()))).getBytes("UTF-8"));//F1_23银行名称
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankAccount()))).getBytes("UTF-8"));//F1_24银行账号
							//"F1_25","F1_26","F1_60","F1_40","F1_41","F1_42","F1_43","F1_44","F1_45","F1_46","F1_47","F1_48",
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_25''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_26''
							buff.write((String.format(sFormat, deccaDetailVO.getHouseHolderisflow())).getBytes("UTF-8"));//F1_60户主是否享受
							buff.write((String.format(sFormat,memberName1)).getBytes("UTF-8"));//F1_40姓名1
							buff.write((String.format(sFormat, memberCard1)).getBytes("UTF-8"));//F1_41身份证1
							buff.write((String.format(sFormat, memberName2)).getBytes("UTF-8"));//F1_42姓名2
							buff.write((String.format(sFormat, memberCard2)).getBytes("UTF-8"));//F1_43身份证2
							buff.write((String.format(sFormat, memberName3)).getBytes("UTF-8"));//F1_44姓名3
							buff.write((String.format(sFormat, memberCard3)).getBytes("UTF-8"));//F1_45身份证3
							buff.write((String.format(sFormat, memberName4)).getBytes("UTF-8"));//F1_46姓名4
							buff.write((String.format(sFormat, memberCard4)).getBytes("UTF-8"));//F1_47身份证4
							buff.write((String.format(sFormat, memberName5)).getBytes("UTF-8"));//F1_48姓名5
							buff.write((String.format(sFormat, memberCard5)).getBytes("UTF-8"));//F1_49身份证5
							buff.write((String.format(sFormat, memberName6)).getBytes("UTF-8"));//F1_50姓名6
							buff.write((String.format(sFormat, memberCard6)).getBytes("UTF-8"));//F1_51身份证6
							buff.write((String.format(sFormat, memberName7)).getBytes("UTF-8"));//F1_52姓名7
							buff.write((String.format(sFormat, memberCard7)).getBytes("UTF-8"));//F1_53身份证7
							buff.write((String.format(sFormat, memberName8)).getBytes("UTF-8"));//F1_54姓名8
							buff.write((String.format(sFormat, memberCard8)).getBytes("UTF-8"));//F1_55身份证8
							buff.write((String.format(sFormat, memberName9)).getBytes("UTF-8"));//F1_56姓名9
							buff.write((String.format(sFormat, memberCard9)).getBytes("UTF-8"));//F1_57
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_27''
							buff.write(nh.getBytes("UTF-8"));
						}
						break;
					case "02"://城保
						String[] hears02 = {"_QYDM","F1_1","F1_2","F1_3","F1_4","F1_5","F1_6","F1_7","F1_8","F1_9","F1_10","F1_11","F1_12",
								"F1_13","F1_61","F1_14","F1_15","F1_16","F1_17","F1_18","F1_19","F1_20","F1_22","F1_23","F1_24","F1_25",
								"F1_26","F1_27","F1_28","F1_29","F1_30","F1_60","F1_40","F1_41","F1_42","F1_43","F1_44","F1_45","F1_46",
								"F1_47","F1_48","F1_49","F1_50","F1_51","F1_52","F1_53","F1_54","F1_55","F1_56","F1_57","F1_31"};
						for (int i = 0; i < hears02.length; i++) {
							buff.write((hears02[i] + rh).getBytes("UTF-8"));
						}
						buff.write("\n".getBytes("UTF-8"));
						//写入内容
						for (MsgsDeccaDetailVO deccaDetailVO : datas) {
							String memberName1=getValue(deccaDetailVO.getMemberName1());
							String memberName2=getValue(deccaDetailVO.getMemberName2());
							String memberName3=getValue(deccaDetailVO.getMemberName3());
							String memberName4=getValue(deccaDetailVO.getMemberName4());
							String memberName5=getValue(deccaDetailVO.getMemberName5());
							String memberName6=getValue(deccaDetailVO.getMemberName6());
							String memberName7=getValue(deccaDetailVO.getMemberName7());
							String memberName8=getValue(deccaDetailVO.getMemberName8());
							String memberName9=getValue(deccaDetailVO.getMemberName9());
							String memberCard1=getValue(deccaDetailVO.getMemberCard1());
							String memberCard2=getValue(deccaDetailVO.getMemberCard2());
							String memberCard3=getValue(deccaDetailVO.getMemberCard3());
							String memberCard4=getValue(deccaDetailVO.getMemberCard4());
							String memberCard5=getValue(deccaDetailVO.getMemberCard5());
							String memberCard6=getValue(deccaDetailVO.getMemberCard6());
							String memberCard7=getValue(deccaDetailVO.getMemberCard7());
							String memberCard8=getValue(deccaDetailVO.getMemberCard8());
							String memberCard9=getValue(deccaDetailVO.getMemberCard9());
							String sexCode= deccaDetailVO.getSexCode()==1?"2":"1";
							String formattedDate = sdf.format(deccaDetailVO.getApplyDate());
							buff.write((String.format(sFormat, deccaDetailVO.getNumberCode())).getBytes("UTF-8"));//_QYDM台卡区划
							buff.write((String.format(sFormat, formattedDate)).getBytes("UTF-8"));//F1_1创建日期
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyId())).getBytes("UTF-8"));//F1_2家庭编号
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholderIdCard())).getBytes("UTF-8"));//F1_3户主身份证
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholder())).getBytes("UTF-8"));//F1_4户主姓名
							buff.write((String.format(sFormat, sexCode)).getBytes("UTF-8"));//F1_5性别code(1男2女)
							buff.write((String.format(sFormat, deccaDetailVO.getNationalityVal())).getBytes("UTF-8"));//F1_6民族
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyAddress())).getBytes("UTF-8"));//F1_7家庭住址（县-村）
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_8空
							buff.write((String.format(sFormat, deccaDetailVO.getTel())).getBytes("UTF-8"));//F1_9联系电话
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_10''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_11''
							buff.write((String.format(sFormat, deccaDetailVO.getWoman())).getBytes("UTF-8"));//F1_12女性
							//"F1_13","F1_61","F1_14","F1_15","F1_16","F1_17","F1_18","F1_19","F1_20","F1_22","F1_23","F1_24","F1_25",
							buff.write((String.format(sFormat, deccaDetailVO.getDisabled())).getBytes("UTF-8"));//F1_13残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getSeverelyDisabled())).getBytes("UTF-8"));//F1_61重度残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getIsThreeNoPerson())).getBytes("UTF-8"));//F1_14三无
							buff.write((String.format(sFormat, deccaDetailVO.getOldPeople())).getBytes("UTF-8"));//F1_15老年人
							buff.write((String.format(sFormat, deccaDetailVO.getAdultWorking())).getBytes("UTF-8"));//F1_16在职
							buff.write((String.format(sFormat, deccaDetailVO.getAdultsflexibleEmployment())).getBytes("UTF-8"));//F1_17灵活就业
							buff.write((String.format(sFormat, deccaDetailVO.getLoseJobAdult())).getBytes("UTF-8"));//F1_18登记失业
							buff.write((String.format(sFormat, deccaDetailVO.getNoLabourCapacity())).getBytes("UTF-8"));//F1_19未登记失业
							buff.write((String.format(sFormat, deccaDetailVO.getJuveniles())).getBytes("UTF-8"));//F1_20未成年
							buff.write((String.format(sFormat, deccaDetailVO.getFinalMoney())).getBytes("UTF-8"));//F1_22发放合计
							buff.write((String.format(sFormat, deccaDetailVO.getMonthIncome())).getBytes("UTF-8"));//F1_23收入
							buff.write((String.format(sFormat, deccaDetailVO.getSubsidyStandard())).getBytes("UTF-8"));//F1_24低保保准金
							buff.write((String.format(sFormat, deccaDetailVO.getClassifyFund())).getBytes("UTF-8"));//F1_25分类施保
							//"F1_26","F1_27","F1_28","F1_29","F1_30","F1_60",
							buff.write((String.format(sFormat, deccaDetailVO.getReliefFund())).getBytes("UTF-8"));//F1_26发放金额
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankVal()))).getBytes("UTF-8"));//F1_23银行名称
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankAccount()))).getBytes("UTF-8"));//F1_24银行账号
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_29''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_30''
							buff.write((String.format(sFormat, deccaDetailVO.getHouseHolderisflow())).getBytes("UTF-8"));//F1_60户主是否享受

							//"F1_40","F1_41","F1_42","F1_43","F1_44","F1_45","F1_46","F1_47","F1_48",
							buff.write((String.format(sFormat, memberName1)).getBytes("UTF-8"));//F1_40姓名1
							buff.write((String.format(sFormat, memberCard1)).getBytes("UTF-8"));//F1_41身份证1
							buff.write((String.format(sFormat, memberName2)).getBytes("UTF-8"));//F1_42姓名2
							buff.write((String.format(sFormat, memberCard2)).getBytes("UTF-8"));//F1_43身份证2
							buff.write((String.format(sFormat, memberName3)).getBytes("UTF-8"));//F1_44姓名3
							buff.write((String.format(sFormat, memberCard3)).getBytes("UTF-8"));//F1_45身份证3
							buff.write((String.format(sFormat, memberName4)).getBytes("UTF-8"));//F1_46姓名4
							buff.write((String.format(sFormat, memberCard4)).getBytes("UTF-8"));//F1_47身份证4
							buff.write((String.format(sFormat, memberName5)).getBytes("UTF-8"));//F1_48姓名5
							buff.write((String.format(sFormat, memberCard5)).getBytes("UTF-8"));//F1_49身份证5
							buff.write((String.format(sFormat, memberName6)).getBytes("UTF-8"));//F1_50姓名6
							buff.write((String.format(sFormat, memberCard6)).getBytes("UTF-8"));//F1_51身份证6
							buff.write((String.format(sFormat, memberName7)).getBytes("UTF-8"));//F1_52姓名7
							buff.write((String.format(sFormat, memberCard7)).getBytes("UTF-8"));//F1_53身份证7
							buff.write((String.format(sFormat, memberName8)).getBytes("UTF-8"));//F1_54姓名8
							buff.write((String.format(sFormat, memberCard8)).getBytes("UTF-8"));//F1_55身份证8
							buff.write((String.format(sFormat, memberName9)).getBytes("UTF-8"));//F1_56姓名9
							buff.write((String.format(sFormat, memberCard9)).getBytes("UTF-8"));//F1_57
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_27''
							buff.write(nh.getBytes("UTF-8"));
						}
						break;
					case "0301"://农村特困
						String[] hears0301 = {"_QYDM","F1_1","F1_2","F1_3","F1_4","F1_5","F1_6","F1_7","F1_8","F1_9","F1_10","F1_11","F1_13","F1_28","F1_14","F1_12",
							"F1_15","F1_70","F1_71","F1_72","F1_16","F1_17","F1_18","F1_19","F1_20","F1_21","F1_23","F1_24","F1_25","F1_26","F1_60","F1_40","F1_41",
							"F1_42","F1_43","F1_44","F1_45","F1_46","F1_47","F1_48","F1_49","F1_50","F1_51","F1_52","F1_53","F1_54","F1_55","F1_56","F1_57","F1_27"};
						for (int i = 0; i < hears0301.length; i++) {
							buff.write((hears0301[i] + rh).getBytes("UTF-8"));
						}
						buff.write("\n".getBytes("UTF-8"));
						//写入内容
						for (MsgsDeccaDetailVO deccaDetailVO : datas) {
							String memberName1=getValue(deccaDetailVO.getMemberName1());
							String memberName2=getValue(deccaDetailVO.getMemberName2());
							String memberName3=getValue(deccaDetailVO.getMemberName3());
							String memberName4=getValue(deccaDetailVO.getMemberName4());
							String memberName5=getValue(deccaDetailVO.getMemberName5());
							String memberName6=getValue(deccaDetailVO.getMemberName6());
							String memberName7=getValue(deccaDetailVO.getMemberName7());
							String memberName8=getValue(deccaDetailVO.getMemberName8());
							String memberName9=getValue(deccaDetailVO.getMemberName9());
							String memberCard1=getValue(deccaDetailVO.getMemberCard1());
							String memberCard2=getValue(deccaDetailVO.getMemberCard2());
							String memberCard3=getValue(deccaDetailVO.getMemberCard3());
							String memberCard4=getValue(deccaDetailVO.getMemberCard4());
							String memberCard5=getValue(deccaDetailVO.getMemberCard5());
							String memberCard6=getValue(deccaDetailVO.getMemberCard6());
							String memberCard7=getValue(deccaDetailVO.getMemberCard7());
							String memberCard8=getValue(deccaDetailVO.getMemberCard8());
							String memberCard9=getValue(deccaDetailVO.getMemberCard9());
							String sexCode= deccaDetailVO.getSexCode()==1?"2":"1";
							String formattedDate = sdf.format(deccaDetailVO.getApplyDate());
							buff.write((String.format(sFormat, deccaDetailVO.getNumberCode())).getBytes("UTF-8"));//_QYDM台卡区划
							buff.write((String.format(sFormat, formattedDate)).getBytes("UTF-8"));//F1_1创建日期
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyId())).getBytes("UTF-8"));//F1_2家庭编号
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholderIdCard())).getBytes("UTF-8"));//F1_3户主身份证
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholder())).getBytes("UTF-8"));//F1_4户主姓名
							buff.write((String.format(sFormat, sexCode)).getBytes("UTF-8"));//F1_5性别code(1男2女)
							buff.write((String.format(sFormat, deccaDetailVO.getNationalityVal())).getBytes("UTF-8"));//F1_6民族
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyAddress())).getBytes("UTF-8"));//F1_7家庭住址（县-村）
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_8空
							buff.write((String.format(sFormat, deccaDetailVO.getTel())).getBytes("UTF-8"));//F1_9联系电话
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_10''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_11''
							buff.write((String.format(sFormat, deccaDetailVO.getOldPeople())).getBytes("UTF-8"));//F1_13老年人
							buff.write((String.format(sFormat, deccaDetailVO.getAdultPeople())).getBytes("UTF-8"));//F1_28年轻人
							buff.write((String.format(sFormat, deccaDetailVO.getJuveniles())).getBytes("UTF-8"));//F1_14未成年人
							buff.write((String.format(sFormat, deccaDetailVO.getWoman())).getBytes("UTF-8"));//F1_12女性
							//"F1_15","F1_70","F1_71","F1_72","F1_16","F1_17","F1_18","F1_19","F1_20","F1_21","F1_23","F1_24","F1_25","F1_26","F1_60","F1_40","F1_41",
							buff.write((String.format(sFormat, deccaDetailVO.getDisabled())).getBytes("UTF-8"));//F1_15残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getAllProvide())).getBytes("UTF-8"));//F1_70全自理
							buff.write((String.format(sFormat, deccaDetailVO.getPartProvide())).getBytes("UTF-8"));//F1_71半自理
							buff.write((String.format(sFormat, deccaDetailVO.getAllCare())).getBytes("UTF-8"));//F1_72全护理
							buff.write((String.format(sFormat, deccaDetailVO.getFinalMoney())).getBytes("UTF-8"));//F1_16发放合计
							buff.write((String.format(sFormat, deccaDetailVO.getFinalMoney())).getBytes("UTF-8"));//F1_17发放合计
							buff.write((String.format(sFormat, deccaDetailVO.getClassifyFund())).getBytes("UTF-8"));//F1_18分类施保
							buff.write((String.format(sFormat, deccaDetailVO.getReliefFund())).getBytes("UTF-8"));//F1_19救助金
							buff.write((String.format(sFormat, deccaDetailVO.getMonthIncome())).getBytes("UTF-8"));//F1_20收入
							buff.write((String.format(sFormat, deccaDetailVO.getSubsidyStandard())).getBytes("UTF-8"));//F1_21低保标准
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankVal()))).getBytes("UTF-8"));//F1_23银行名称
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankAccount()))).getBytes("UTF-8"));//F1_24银行账号
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_25''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_26''
							buff.write((String.format(sFormat, deccaDetailVO.getHouseHolderisflow())).getBytes("UTF-8"));//F1_60户主是否享受

							buff.write((String.format(sFormat, memberName1)).getBytes("UTF-8"));//F1_40姓名1
							buff.write((String.format(sFormat, memberCard1)).getBytes("UTF-8"));//F1_41身份证1
							buff.write((String.format(sFormat, memberName2)).getBytes("UTF-8"));//F1_42姓名2
							buff.write((String.format(sFormat, memberCard2)).getBytes("UTF-8"));//F1_43身份证2
							buff.write((String.format(sFormat, memberName3)).getBytes("UTF-8"));//F1_44姓名3
							buff.write((String.format(sFormat, memberCard3)).getBytes("UTF-8"));//F1_45身份证3
							buff.write((String.format(sFormat, memberName4)).getBytes("UTF-8"));//F1_46姓名4
							buff.write((String.format(sFormat, memberCard4)).getBytes("UTF-8"));//F1_47身份证4
							buff.write((String.format(sFormat, memberName5)).getBytes("UTF-8"));//F1_48姓名5
							buff.write((String.format(sFormat, memberCard5)).getBytes("UTF-8"));//F1_49身份证5
							buff.write((String.format(sFormat, memberName6)).getBytes("UTF-8"));//F1_50姓名6
							buff.write((String.format(sFormat, memberCard6)).getBytes("UTF-8"));//F1_51身份证6
							buff.write((String.format(sFormat, memberName7)).getBytes("UTF-8"));//F1_52姓名7
							buff.write((String.format(sFormat, memberCard7)).getBytes("UTF-8"));//F1_53身份证7
							buff.write((String.format(sFormat, memberName8)).getBytes("UTF-8"));//F1_54姓名8
							buff.write((String.format(sFormat, memberCard8)).getBytes("UTF-8"));//F1_55身份证8
							buff.write((String.format(sFormat, memberName9)).getBytes("UTF-8"));//F1_56姓名9
							buff.write((String.format(sFormat, memberCard9)).getBytes("UTF-8"));//F1_57
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_27''
							buff.write(nh.getBytes("UTF-8"));
						}
						break;
					case "0303"://城市特困
						String[] hears0303 = {"_QYDM","F1_1","F1_2","F1_3","F1_4","F1_5","F1_6","F1_7","F1_8","F1_9","F1_10","F1_11","F1_12","F1_13","F1_14","F1_15",
							"F1_16","F1_70","F1_71","F1_72","F1_17","F1_18","F1_19","F1_20","F1_21","F1_22","F1_23","F1_24","F1_25","F1_26","F1_60","F1_40","F1_41",
							"F1_42","F1_43","F1_44","F1_45","F1_46","F1_47","F1_48","F1_49","F1_50","F1_51","F1_52","F1_53","F1_54","F1_55","F1_56","F1_57","F1_27"};
						for (int i = 0; i < hears0303.length; i++) {
							buff.write((hears0303[i] + rh).getBytes("UTF-8"));
						}
						buff.write("\n".getBytes("UTF-8"));
						//写入内容
						for (MsgsDeccaDetailVO deccaDetailVO : datas) {
							String memberName1=getValue(deccaDetailVO.getMemberName1());
							String memberName2=getValue(deccaDetailVO.getMemberName2());
							String memberName3=getValue(deccaDetailVO.getMemberName3());
							String memberName4=getValue(deccaDetailVO.getMemberName4());
							String memberName5=getValue(deccaDetailVO.getMemberName5());
							String memberName6=getValue(deccaDetailVO.getMemberName6());
							String memberName7=getValue(deccaDetailVO.getMemberName7());
							String memberName8=getValue(deccaDetailVO.getMemberName8());
							String memberName9=getValue(deccaDetailVO.getMemberName9());
							String memberCard1=getValue(deccaDetailVO.getMemberCard1());
							String memberCard2=getValue(deccaDetailVO.getMemberCard2());
							String memberCard3=getValue(deccaDetailVO.getMemberCard3());
							String memberCard4=getValue(deccaDetailVO.getMemberCard4());
							String memberCard5=getValue(deccaDetailVO.getMemberCard5());
							String memberCard6=getValue(deccaDetailVO.getMemberCard6());
							String memberCard7=getValue(deccaDetailVO.getMemberCard7());
							String memberCard8=getValue(deccaDetailVO.getMemberCard8());
							String memberCard9=getValue(deccaDetailVO.getMemberCard9());
							String sexCode= deccaDetailVO.getSexCode()==1?"2":"1";
							String formattedDate = sdf.format(deccaDetailVO.getApplyDate());
							buff.write((String.format(sFormat, deccaDetailVO.getNumberCode())).getBytes("UTF-8"));//_QYDM台卡区划
							buff.write((String.format(sFormat, formattedDate)).getBytes("UTF-8"));//F1_1创建日期
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyId())).getBytes("UTF-8"));//F1_2家庭编号
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholderIdCard())).getBytes("UTF-8"));//F1_3户主身份证
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholder())).getBytes("UTF-8"));//F1_4户主姓名
							buff.write((String.format(sFormat, sexCode)).getBytes("UTF-8"));//F1_5性别code(1男2女)
							buff.write((String.format(sFormat, deccaDetailVO.getNationalityVal())).getBytes("UTF-8"));//F1_6民族
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyAddress())).getBytes("UTF-8"));//F1_7家庭住址（县-村）
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_8空
							buff.write((String.format(sFormat, deccaDetailVO.getTel())).getBytes("UTF-8"));//F1_9联系电话
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_10''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_11''
							buff.write((String.format(sFormat, deccaDetailVO.getOldPeople())).getBytes("UTF-8"));//F1_12老年人
							buff.write((String.format(sFormat, deccaDetailVO.getAdultPeople())).getBytes("UTF-8"));//F1_13年轻人
							buff.write((String.format(sFormat, deccaDetailVO.getJuveniles())).getBytes("UTF-8"));//F1_14未成年人
							buff.write((String.format(sFormat, deccaDetailVO.getWoman())).getBytes("UTF-8"));//F1_15女性
							//"F1_16","F1_70","F1_71","F1_72","F1_17","F1_18","F1_19","F1_20","F1_21","F1_22","F1_23","F1_24","F1_25","F1_26","F1_60","F1_40","F1_41",
							buff.write((String.format(sFormat, deccaDetailVO.getDisabled())).getBytes("UTF-8"));//F1_16残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getAllProvide())).getBytes("UTF-8"));//F1_70全自理
							buff.write((String.format(sFormat, deccaDetailVO.getPartProvide())).getBytes("UTF-8"));//F1_71半自理
							buff.write((String.format(sFormat, deccaDetailVO.getAllCare())).getBytes("UTF-8"));//F1_72全护理
							buff.write((String.format(sFormat, deccaDetailVO.getFinalMoney())).getBytes("UTF-8"));//F1_17发放合计
							buff.write((String.format(sFormat, deccaDetailVO.getFinalMoney())).getBytes("UTF-8"));//F1_18发放合计
							buff.write((String.format(sFormat, deccaDetailVO.getClassifyFund())).getBytes("UTF-8"));//F1_19分类施保
							buff.write((String.format(sFormat, deccaDetailVO.getReliefFund())).getBytes("UTF-8"));//F1_20救助金
							buff.write((String.format(sFormat, deccaDetailVO.getMonthIncome())).getBytes("UTF-8"));//F1_21收入
							buff.write((String.format(sFormat, deccaDetailVO.getSubsidyStandard())).getBytes("UTF-8"));//F1_22低保标准
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankVal()))).getBytes("UTF-8"));//F1_23银行名称
							buff.write((String.format(sFormat, getValue(deccaDetailVO.getBankAccount()))).getBytes("UTF-8"));//F1_24银行账号
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_25''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_26''
							buff.write((String.format(sFormat, deccaDetailVO.getHouseHolderisflow())).getBytes("UTF-8"));//F1_60户主是否享受

							buff.write((String.format(sFormat, memberName1)).getBytes("UTF-8"));//F1_40姓名1
							buff.write((String.format(sFormat, memberCard1)).getBytes("UTF-8"));//F1_41身份证1
							buff.write((String.format(sFormat, memberName2)).getBytes("UTF-8"));//F1_42姓名2
							buff.write((String.format(sFormat, memberCard2)).getBytes("UTF-8"));//F1_43身份证2
							buff.write((String.format(sFormat, memberName3)).getBytes("UTF-8"));//F1_44姓名3
							buff.write((String.format(sFormat, memberCard3)).getBytes("UTF-8"));//F1_45身份证3
							buff.write((String.format(sFormat, memberName4)).getBytes("UTF-8"));//F1_46姓名4
							buff.write((String.format(sFormat, memberCard4)).getBytes("UTF-8"));//F1_47身份证4
							buff.write((String.format(sFormat, memberName5)).getBytes("UTF-8"));//F1_48姓名5
							buff.write((String.format(sFormat, memberCard5)).getBytes("UTF-8"));//F1_49身份证5
							buff.write((String.format(sFormat, memberName6)).getBytes("UTF-8"));//F1_50姓名6
							buff.write((String.format(sFormat, memberCard6)).getBytes("UTF-8"));//F1_51身份证6
							buff.write((String.format(sFormat, memberName7)).getBytes("UTF-8"));//F1_52姓名7
							buff.write((String.format(sFormat, memberCard7)).getBytes("UTF-8"));//F1_53身份证7
							buff.write((String.format(sFormat, memberName8)).getBytes("UTF-8"));//F1_54姓名8
							buff.write((String.format(sFormat, memberCard8)).getBytes("UTF-8"));//F1_55身份证8
							buff.write((String.format(sFormat, memberName9)).getBytes("UTF-8"));//F1_56姓名9
							buff.write((String.format(sFormat, memberCard9)).getBytes("UTF-8"));//F1_57
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_27''
							buff.write(nh.getBytes("UTF-8"));
						}
						break;
					case "0407"://城市低边
					case "0408"://农村低边
						String[] hears0407 = {"_QYDM","F1_1","F1_2","F1_3","F1_4","F1_5","F1_6","F1_7","F1_8","F1_9","F1_11","F1_14","F1_15","F1_40","F1_41","F1_42",
							"F1_43","F1_44","F1_45","F1_46","F1_47","F1_48","F1_49","F1_50","F1_51","F1_52","F1_53","F1_54","F1_55","F1_56","F1_57","F1_58","F1_59",
							"F1_65","F1_66","F1_67","F1_68","F1_69","F1_70","F1_71","F1_72","F1_73","F1_74","F1_75","F1_76","F1_77","F1_78","F1_79","F1_80","F1_81",
							"F1_82","F1_83","F1_84","FLD_1","F1_31"};
						for (int i = 0; i < hears0407.length; i++) {
							buff.write((hears0407[i] + rh).getBytes("UTF-8"));
						}
						buff.write("\n".getBytes("UTF-8"));
						//写入内容
						for (MsgsDeccaDetailVO deccaDetailVO : datas) {
							String memberName1=getValue(deccaDetailVO.getMemberName1());
							String memberName2=getValue(deccaDetailVO.getMemberName2());
							String memberName3=getValue(deccaDetailVO.getMemberName3());
							String memberName4=getValue(deccaDetailVO.getMemberName4());
							String memberName5=getValue(deccaDetailVO.getMemberName5());
							String memberName6=getValue(deccaDetailVO.getMemberName6());
							String memberName7=getValue(deccaDetailVO.getMemberName7());
							String memberName8=getValue(deccaDetailVO.getMemberName8());
							String memberName9=getValue(deccaDetailVO.getMemberName9());
							String memberCard1=getValue(deccaDetailVO.getMemberCard1());
							String memberCard2=getValue(deccaDetailVO.getMemberCard2());
							String memberCard3=getValue(deccaDetailVO.getMemberCard3());
							String memberCard4=getValue(deccaDetailVO.getMemberCard4());
							String memberCard5=getValue(deccaDetailVO.getMemberCard5());
							String memberCard6=getValue(deccaDetailVO.getMemberCard6());
							String memberCard7=getValue(deccaDetailVO.getMemberCard7());
							String memberCard8=getValue(deccaDetailVO.getMemberCard8());
							String memberCard9=getValue(deccaDetailVO.getMemberCard9());
							String sexCode= deccaDetailVO.getSexCode()==1?"2":"1";
							String formattedDate = sdf.format(deccaDetailVO.getApplyDate());
							buff.write((String.format(sFormat, deccaDetailVO.getNumberCode())).getBytes("UTF-8"));//_QYDM台卡区划
							buff.write((String.format(sFormat, formattedDate)).getBytes("UTF-8"));//F1_1创建日期
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyId())).getBytes("UTF-8"));//F1_2家庭编号
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholderIdCard())).getBytes("UTF-8"));//F1_3户主身份证
							buff.write((String.format(sFormat, deccaDetailVO.getHouseholder())).getBytes("UTF-8"));//F1_4户主姓名
							buff.write((String.format(sFormat, sexCode)).getBytes("UTF-8"));//F1_5性别code(1男2女)
							buff.write((String.format(sFormat, deccaDetailVO.getNationalityVal())).getBytes("UTF-8"));//F1_6民族
							buff.write((String.format(sFormat, deccaDetailVO.getFamilyAddress())).getBytes("UTF-8"));//F1_7家庭住址（县-村）
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_8空
							buff.write((String.format(sFormat, deccaDetailVO.getTel())).getBytes("UTF-8"));//F1_9联系电话
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_11''
							buff.write((String.format(sFormat, deccaDetailVO.getDisabled())).getBytes("UTF-8"));//F1_14残疾人
							buff.write((String.format(sFormat, deccaDetailVO.getWoman())).getBytes("UTF-8"));//F1_15女性

							buff.write((String.format(sFormat, memberName1)).getBytes("UTF-8"));//F1_40姓名1
							buff.write((String.format(sFormat, memberCard1)).getBytes("UTF-8"));//F1_41身份证1
							buff.write((String.format(sFormat, memberName2)).getBytes("UTF-8"));//F1_42姓名2
							buff.write((String.format(sFormat, memberCard2)).getBytes("UTF-8"));//F1_43身份证2
							buff.write((String.format(sFormat, memberName3)).getBytes("UTF-8"));//F1_44姓名3
							buff.write((String.format(sFormat, memberCard3)).getBytes("UTF-8"));//F1_45身份证3
							buff.write((String.format(sFormat, memberName4)).getBytes("UTF-8"));//F1_46姓名4
							buff.write((String.format(sFormat, memberCard4)).getBytes("UTF-8"));//F1_47身份证4
							buff.write((String.format(sFormat, memberName5)).getBytes("UTF-8"));//F1_48姓名5
							buff.write((String.format(sFormat, memberCard5)).getBytes("UTF-8"));//F1_49身份证5
							buff.write((String.format(sFormat, memberName6)).getBytes("UTF-8"));//F1_50姓名6
							buff.write((String.format(sFormat, memberCard6)).getBytes("UTF-8"));//F1_51身份证6
							buff.write((String.format(sFormat, memberName7)).getBytes("UTF-8"));//F1_52姓名7
							buff.write((String.format(sFormat, memberCard7)).getBytes("UTF-8"));//F1_53身份证7
							buff.write((String.format(sFormat, memberName8)).getBytes("UTF-8"));//F1_54姓名8
							buff.write((String.format(sFormat, memberCard8)).getBytes("UTF-8"));//F1_55身份证8
							buff.write((String.format(sFormat, memberName9)).getBytes("UTF-8"));//F1_56姓名9
							buff.write((String.format(sFormat, memberCard9)).getBytes("UTF-8"));//F1_57

							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_58''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_59''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_65''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_66''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_67''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_68''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_69''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_70''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_71''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_72''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_73''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_74''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_75''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_76''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_77''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_78''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_79''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_80''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_81''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_82''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_83''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_84''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//FLD_1''
							buff.write((String.format(sFormat, kong)).getBytes("UTF-8"));//F1_31''

							buff.write(nh.getBytes("UTF-8"));
						}
						break;

				}

				buff.flush();
				buff.close();
			} catch (Exception e) {
				log.warn("数据导出失败", e);
			} finally {
				try {
					buff.close();
					outStr.close();
				} catch (Exception e) {
					log.warn("数据导出失败", e);
				}
			}
		}
	}

	public String getValue(String name) {
		if(StringUtils.isEmpty(name)) {
			return " ";
		}else {
			return name;
		}
	}

}