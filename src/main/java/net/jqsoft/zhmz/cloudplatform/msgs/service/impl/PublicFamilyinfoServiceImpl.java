package net.jqsoft.zhmz.cloudplatform.msgs.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.fsg.uid.UidGenerator;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import net.jqsoft.apis.util.QueryParams;
import net.jqsoft.common.lang.Converter;
import net.jqsoft.common.lang.StringTools;
import net.jqsoft.persist.mybatisplus.query.Page;
import net.jqsoft.persist.mybatisplus.query.QueryWrapper;
import net.jqsoft.persist.mybatisplus.query.UpdateWrapper;
import net.jqsoft.persist.mybatisplus.support.MySuperServiceImpl;
import net.jqsoft.persist.mybatisplus.support.QueryActuator;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.crud.BaseEntity;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.FamilyRelation;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.FileCategory;
import net.jqsoft.zhmz.cloudplatform.msgs.config.ProjectProperties;
import net.jqsoft.zhmz.cloudplatform.msgs.exception.BusinessException;
import net.jqsoft.zhmz.cloudplatform.msgs.mapper.PublicFamilyMemberinfoMapper;
import net.jqsoft.zhmz.cloudplatform.msgs.mapper.PublicFamilyinfoMapper;
import net.jqsoft.zhmz.cloudplatform.msgs.model.dto.memberauth.AgentAuthDto;
import net.jqsoft.zhmz.cloudplatform.msgs.model.dto.memberauth.AssistanceApplicationDto;
import net.jqsoft.zhmz.cloudplatform.msgs.model.dto.memberauth.MemberAuthDto;
import net.jqsoft.zhmz.cloudplatform.msgs.model.dto.memberauth.MemberCheckAuthDto;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.*;
import net.jqsoft.zhmz.cloudplatform.msgs.model.vo.*;
import net.jqsoft.zhmz.cloudplatform.msgs.service.*;
import net.jqsoft.zhmz.cloudplatform.msgs.sri.model.entity.TassAttachmentInfo;
import net.jqsoft.zhmz.cloudplatform.msgs.sri.model.entity.TassFamilyInfo;
import net.jqsoft.zhmz.cloudplatform.msgs.sri.model.entity.TassFamilyMemberInfo;
import net.jqsoft.zhmz.cloudplatform.msgs.sri.service.ITassAttachmentInfoService;
import net.jqsoft.zhmz.cloudplatform.msgs.sri.service.ITassFamilyInfoService;
import net.jqsoft.zhmz.cloudplatform.msgs.sri.service.ITassFamilyMemberInfoService;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.*;
import net.jqsoft.zhmz.common.vo.UserVO;
import org.jsets.fastboot.security.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 综合受理 - 家庭基础信息表（仅录入简洁版数据，完整版数据在各自业务的认定表中完成）;(PUBLIC_FAMILYINFO)表服务实现类
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-11-7
 */
@Slf4j
@Service
public class PublicFamilyinfoServiceImpl extends MySuperServiceImpl<PublicFamilyinfo, PublicFamilyinfoMapper> implements IPublicFamilyinfoService {

    // <editor-fold desc="加载服务">
    @Autowired
    IMsgsFamilymemberinfoService memberService;
    @Autowired
    IMsgsFamilyinfoService familyInfoService;
    @Autowired
    IPublicFamilyMemberinfoService publicMemberService;
    @Autowired
    IMsgsProviderinfoService providerService;
    @Autowired
    IPublicAttachmentinfoService attachmentService;
    @Autowired
    IPublicFamilyDistributionService distributionService;
    @Autowired
    IMsgsSurveyAgentService msgsSurveyAgentService;
    @Autowired
    UidGenerator generator;
    @Autowired
    IMsgsAttachmentinfoService attachmentInfoService;
    @Autowired
    ITassFamilyInfoService tassFamilyInfoService;
    @Autowired
    IPublicProviderinfoService publicProviderService;
    @Autowired
    IMsgsCheckDetailService checkDetailService;
    @Autowired
    ISaFeedbackService saFeedbackService;
    @Autowired
    ITassFamilyMemberInfoService tassFamilyMemberInfoService;
    @Autowired
    ITassAttachmentInfoService tassAttachmentInfoService;
    @Autowired
    IPublicSurveyAgentService agentService;
    @Autowired
    IMemberCheckAuthService memberCheckAuthService;
    @Autowired
    PublicFamilyMemberinfoMapper memberInfoMapper;
    @Autowired
    ProjectProperties projectProperties;
    @Autowired(required = false)
    IUserContextService userContextService;
    @Autowired(required = false)
    UserContextDebugUtil userContextDebugUtil;
    // </editor-fold>

    @Override
    public List<PublicFamilyinfoVO> selectPublicFamilyinfoList(QueryParams conditions) {
        return this.baseMapper.selectPageList(conditions);
    }

    @Override
    public Integer getcount(QueryParams conditions) {
        return this.baseMapper.getcount(conditions);
    }

    @Override
    public Page<PublicFamilyinfo> Page(QueryParams conditions) {
        conditions.setPageNum(conditions.getPageNum()); // 强制分页标识
        return (Page<PublicFamilyinfo>) this.selectList(conditions);
    }

    @Override
    public List<PublicFamilyinfo> List(QueryParams conditions) {
        conditions.setPageNum(null); // 防止分页
        conditions.setPageSize(null);
        return (List<PublicFamilyinfo>) this.selectList(conditions);
    }

    private Object selectList(QueryParams conditions) {
        String areaCode = Converter.toString(conditions.get("areaCode"));
        Integer areaLevel = Converter.toInteger(conditions.get("areaLevel"));
        String peopleName = Converter.toString(conditions.get("peopleName"));
        Integer status = Converter.toInteger(conditions.get("status"));
        Integer dataSource = Converter.toInteger(conditions.get("dataSource"));
        Integer ismobile = Converter.toInteger(conditions.get("ismobile"));
        String createuserid = Converter.toString(conditions.get("createuserid"));
        String loginIdCard = Converter.toString(conditions.get("loginIdCard"));
        Integer householdStatus = Converter.toInteger(conditions.get("householdStatus"));
        Integer isWarning = Converter.toInteger(conditions.get("isWarning"));
        Integer isWithChildren = Converter.toInteger(conditions.get("isWithChildren"));
        // 是否加载每项进度
        Integer isProgress = Converter.toInteger(conditions.get("isProgress"));
        boolean doPage = (conditions.getPageNum() != null && conditions.getPageSize() != null);

        QueryActuator<PublicFamilyinfo> querier = this.querier().eq("DELETE_MARK", 0).eq(areaLevel != null && areaLevel == 1, "PROVINCE_CODE", areaCode).eq(areaLevel != null && areaLevel == 2, "CITY_CODE", areaCode).eq(areaLevel != null && areaLevel == 3, "COUNTY_CODE", areaCode).eq(areaLevel != null && areaLevel == 4, "TOWN_CODE", areaCode).eq(areaLevel != null && areaLevel == 5, "VILLAGE_CODE", areaCode).eq(dataSource != null, "DATA_SOURCE", dataSource).eq(ismobile != null && ismobile == 1 && dataSource != null && dataSource == 3, "CREATE_USER_ID", createuserid).eq(householdStatus != null, "HOUSEHOLD_STATUS", householdStatus).eq(isWarning != null, "IS_WARNING", isWarning).notIn(ismobile != null && ismobile == 1 && dataSource == null, "DATA_SOURCE", 3).notIn(ismobile != null && ismobile == 1 && status != null && status != 0, "AUDIT_STATUS", 0).eq(status != null && (ismobile == null || ismobile == 1 && status == 0), "AUDIT_STATUS", status).like(StringUtils.isNotEmpty(peopleName), "HOUSEHOLDER", peopleName).orderByDesc("MODIFY_DATE", "VILLAGE_CODE");

        if (StringTools.notEmpty(loginIdCard)) {
            querier.apply("( TRUSTOR_ID_CARD = {0} OR HOUSEHOLDER_ID_CARD = {0} )", loginIdCard);
        }

        if (doPage) {
            querier.setCurrentPage(conditions.getPageNum()).setPageSize(conditions.getPageSize());
            Page<PublicFamilyinfo> page = querier.page();
            // <editor-fold desc="分页时，绑定附加数据">
            if (isWithChildren != null && isWithChildren == 1) {
                enrichFamilyInfoBatch(page.getRecords());
            }
            // <editor-fold desc="绑定进度">
            if (isProgress != null && isProgress == 1) {
                // 提取 ID 集合
                List<String> ids = page.getRecords().stream().map(BaseEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ids)) {
                    return page;
                }

                // 执行分组统计
                Map<String, Object> params = new HashMap<>();
                params.put("familyIds", ids);
                List<GroupStatVO<String, Integer>> groupStats = memberInfoMapper.countGroupByFamilyId(params);

                // 将统计结果按 * 分组
                Map<String, List<GroupStatVO<String, Integer>>> groupedStats = Optional.ofNullable(groupStats).orElse(Collections.emptyList()).stream().filter(vo -> vo.getKey() != null).collect(Collectors.groupingBy(GroupStatVO::getKey));
                for (PublicFamilyinfo record : page.getRecords()) {
                    List<GroupStatVO<String, Integer>> recordStats = groupedStats.getOrDefault(record.getId(), Collections.emptyList());
                    ProgressVO progress = new ProgressVO();
                    progress.setCode("Check");
                    progress.setCompletedCount(0);
                    progress.setTotalCount(0);
                    if (CollectionUtils.isNotEmpty(recordStats)) {
                        int totalCount = recordStats.stream().filter(vo -> vo.getValue() != null).mapToInt(GroupStatVO::getValue).sum();
                        int completedCount = recordStats.stream().filter(vo -> Objects.equals(vo.getName(), "2") && vo.getValue() != null).mapToInt(GroupStatVO::getValue).sum();
                        progress.setTotalCount(totalCount);
                        progress.setCompletedCount(completedCount);
                    }
                    record.setProgress(Collections.singletonList(progress));
                }
            }

            // </editor-fold>
            // </editor-fold>
            return page;
        }
        return querier.list();
    }

    private void enrichFamilyInfoBatch(List<PublicFamilyinfo> familyList) {
        if (CollectionUtils.isEmpty(familyList)) return;

        List<String> familyIds = familyList.stream().map(PublicFamilyinfo::getId).filter(Objects::nonNull).collect(Collectors.toList());

        // 构建 ID -> List 映射，提高访问效率
        Map<String, List<PublicProviderinfo>> providerMap = publicProviderService.querier().in("FAMILY_ID", familyIds).eq("DELETE_MARK", 0).list().stream().collect(Collectors.groupingBy(PublicProviderinfo::getFamilyId));

        Map<String, List<PublicFamilyMemberinfo>> memberMap = publicMemberService.querier().in("FAMILY_ID", familyIds).eq("DELETE_MARK", 0).list().stream().collect(Collectors.groupingBy(PublicFamilyMemberinfo::getFamilyId));

        Map<String, List<PublicFamilyDistribution>> distributionMap = distributionService.querier().in("FAMILY_ID", familyIds).eq("DELETE_MARK", 0).list().stream().collect(Collectors.groupingBy(PublicFamilyDistribution::getFamilyId));

        Map<String, List<PublicAttachmentinfo>> attachmentMap = attachmentService.querier().in("OBJECT_ID", familyIds).eq("DELETE_MARK", 0).list().stream().collect(Collectors.groupingBy(PublicAttachmentinfo::getObjectId));

        for (PublicFamilyinfo family : familyList) {
            String familyId = family.getId();
            family.setMsgsProviderinfoList(providerMap.getOrDefault(familyId, Collections.emptyList()));
            family.setPublicFamilyMemberinfoList(memberMap.getOrDefault(familyId, Collections.emptyList()));
            family.setPublicFamilyDistributionList(distributionMap.getOrDefault(familyId, Collections.emptyList()));
            family.setPublicAttachmentinfoList(attachmentMap.getOrDefault(familyId, Collections.emptyList()));
        }
    }

    @Override
    public Page<PublicFamilyinfoVO> selectPublicFamilyinfoPage(QueryParams conditions) {
        Page<PublicFamilyinfoVO> page = new Page<>(conditions.getPageNum(), conditions.getPageSize());
        page = this.baseMapper.selectPageList(page, conditions);
        return page;
    }

    @Override
    public List<FamilyMemberInfoVO> selectFamilyMemberinfoList(QueryParams conditions) {
        return this.baseMapper.selectFamilyMemberinfoList(conditions);
    }

    @Override
    public List<FamilyMemberInfoVO> selectPublicFamilyMemberinfoList(QueryParams conditions) {
        return this.baseMapper.selectPublicFamilyMemberinfoList(conditions);
    }

    @Override
    @Transactional
    public void auditData(PublicFamilyinfo entity) throws IllegalAccessException {
        UserVO userVO = SecurityUtils.getAccount();
        Date now = new Date();
        entity.setModifyDate(now);
        entity.setModifyUserId(userVO.getId());
        entity.setModifyUserName(userVO.getRealName());
        this.update(entity);
    }

    @Override
    public PublicFamilyinfo Find(String id) {
        return this.Find(id, 1);
    }

    @Override
    public PublicFamilyinfo Find(String id, int bindAgentMethod) {
        PublicFamilyinfo familyInfo = this.selectOne(id);
        if (familyInfo != null) {
            List<PublicFamilyMemberinfo> memberInfos = publicMemberService.selectByFamilyId(id);
            List<PublicSurveyAgent> agents = agentService.selectByFamilyId(id);
            List<PublicAttachmentinfo> attachmentInfos = attachmentService.selectByFamilyId(id);
            // 绑定成员附件
            for (PublicFamilyMemberinfo memberInfo : memberInfos) {
                List<PublicAttachmentinfo> memberAttachmentInfos = attachmentInfos.stream().filter(t -> t.getMemberId() != null && t.getMemberId().equals(memberInfo.getId())).collect(Collectors.toList());
                memberInfo.setAttachmentinfoList(memberAttachmentInfos);

                if (bindAgentMethod == 2) {
                    agents.stream().filter(t -> t.getMemberId() != null && t.getMemberId().equals(memberInfo.getId())).findFirst().ifPresent(memberInfo::setAgent);
                }
            }
            familyInfo.setPublicSurveyAgentList(agents);
            familyInfo.setMsgsProviderinfoList(publicProviderService.selectByFamilyId(id));
            familyInfo.setPublicFamilyMemberinfoList(memberInfos);
            familyInfo.setPublicFamilyDistributionList(distributionService.selectByFamilyId(id));
            familyInfo.setPublicAttachmentinfoList(attachmentInfos);
        }
        return familyInfo;
    }

    @Override
    public PublicFamilyinfo specFind(String id, String familyCategoryVal) {
        PublicFamilyDistribution familyDistribution = distributionService.selectOne(id);
        PublicFamilyinfo familyinfo = this.selectOne(familyDistribution.getFamilyId());
        if (familyinfo != null) {
            familyinfo.setMsgsProviderinfoList(publicProviderService.querier().eq("FAMILY_ID", familyinfo.getId()).eq("DELETE_MARK", 0).list());
            familyinfo.setPublicFamilyMemberinfoList(publicMemberService.querier().eq("FAMILY_ID", familyinfo.getId()).eq("DELETE_MARK", 0).list());
            familyinfo.setPublicAttachmentinfoList(attachmentService.querier().eq("OBJECT_ID", familyinfo.getId()).eq("DELETE_MARK", 0).list());
            List<PublicFamilyDistribution> publicFamilyDistributions = new ArrayList<>();
            publicFamilyDistributions.add(familyDistribution);
            familyinfo.setPublicFamilyDistributionList(publicFamilyDistributions);
            SaFeedback saFeedback = saFeedbackService.selectOne(new QueryWrapper<SaFeedback>().eq("DISTRIBUTION_ID", id).eq("DELETE_MARK", 0));
            familyinfo.setSaFeedback(saFeedback);
            if (saFeedback.getResult() > 0) {//有反馈结果信息
                familyinfo.setAuditStatus(10);//办结状态
            }
        }
        return familyinfo;
    }

    // <editor-fold desc="验证、保存数据">

    /**
     * 验证申请信息
     */
    @Override
    public void validateApplication(PublicFamilyinfo entity) {
        List<PublicFamilyMemberinfo> memberList = entity.getPublicFamilyMemberinfoList();
        List<PublicFamilyDistribution> distributionList = entity.getPublicFamilyDistributionList();

        // 校验家庭成员是否为空
        if (memberList == null || memberList.isEmpty()) {
            throw new BusinessException("操作失败，至少添加一位家庭成员");
        }

        // 校验救助类型是否选择
        if (distributionList == null || distributionList.isEmpty()) {
            throw new BusinessException("操作失败，请选择救助类型");
        }

        // ========================== 校验户主一致性 ================================
        int householdCount = 0;
        boolean householderMatch = true;

        for (PublicFamilyMemberinfo member : memberList) {
            if (FamilyRelation.HOUSEHOLDER.getCode().equals(member.getFamilyRelationVal())) {
                householdCount++;

                if (!member.getPersonName().equals(entity.getHouseholder()) || !member.getPersonIdCard().equals(entity.getHouseholderIdCard())) {
                    householderMatch = false;
                }
            }
        }

        if (householdCount == 0) {
            throw new BusinessException("操作失败，请添加户主!");
        }
        if (householdCount > 1) {
            throw new BusinessException("操作失败，户主只能有一位!");
        }
        if (!householderMatch) {
            throw new BusinessException("操作失败，申请人姓名和身份证号与家庭成员户主的信息需要一致!");
        }

        // ======================= 校验重复申请条件 ============================
        Set<String> repeatSensitiveSet = new HashSet<>(Arrays.asList("01", "02", "03", "04", "05", "06"));

        // 如果包含敏感救助类型，则校验每个成员
        boolean hasSensitiveType = distributionList.stream().anyMatch(dist -> repeatSensitiveSet.contains(dist.getFamilyCategoryVal()));

        if (hasSensitiveType) {
            for (PublicFamilyMemberinfo member : memberList) {
                checkRepeatApply(member.getPersonIdCard(), entity.getId());
            }
        }
    }

    /**
     * 校验指定家庭成员是否已申请或正在享受敏感救助类型。
     */
    @Override
    public void checkRepeatApply(String idcard, String familyid) {
        QueryParams params = QueryParams.build();
        params.add("PERSON_ID_CARD", idcard);
        params.add("familyid", familyid);

        // 1. 校验认定/享受状态（历史记录）
        List<FamilyMemberInfoVO> list = this.selectFamilyMemberinfoList(params);
        if (list != null && !list.isEmpty()) {
            // 优先获取 dataStatus 为 "1" 的记录
            FamilyMemberInfoVO info = list.stream().filter(vo -> "1".equals(vo.getDataStatus())).findFirst().orElse(list.get(0));

            // 统一格式化信息
            String status = "0".equals(info.getDataStatus()) ? "申请中" : "显示在保";
            String msg = String.format("检测到身份证号%s已是低收入人口：<br/>" + "家庭分类救助类别：%s<br/>" + "状态：%s<br/>" + "所属地区：%s", info.getPersonIdCard(), info.getFamilyCategoryKey() != null ? info.getFamilyCategoryKey() : "暂无", status, info.getFullAreaName() != null ? info.getFullAreaName() : "暂无");

            throw new BusinessException(msg);
        }

        // 2. 校验受理中状态（公共信息）
        List<FamilyMemberInfoVO> list1 = this.selectPublicFamilyMemberinfoList(params);
        if (list1 != null && !list1.isEmpty()) {
            FamilyMemberInfoVO info = list1.stream().filter(vo -> "1".equals(vo.getDataStatus())).findFirst().orElse(list1.get(0));

            String msg = String.format("检测到身份证号%s已是低收入人口：<br/>" + "家庭分类救助类别：%s<br/>" + "状态：正在受理<br/>" + "所属地区：%s", info.getPersonIdCard(), info.getFamilyCategoryKey() != null ? info.getFamilyCategoryKey() : "暂无", info.getFullAreaName() != null ? info.getFullAreaName() : "暂无");
            throw new BusinessException(msg);
        }
    }

    @Override
    @Transactional
    public void saveData(PublicFamilyinfo entity) throws Exception {
        saveOrUpdateWithChildren(entity, 1, 1, 1);
    }

    @Transactional
    @Override
    public void updateData(PublicFamilyinfo entity) throws Exception {
        saveOrUpdateWithChildren(entity, 2, 1, 1);
    }

    /**
     * 保存或更新家庭信息数据。
     *
     * @param entity          家庭信息实体
     * @param operType        操作类型：1-新增，2-编辑
     * @param saveAgentMethod 代理人处理方式：1-绑定家庭保存，2-绑定成员保存
     * @param isValid         是否验证：0-否、1-是，
     */
    @Transactional
    @Override
    public void saveOrUpdateWithChildren(PublicFamilyinfo entity, int operType, int saveAgentMethod, int isValid) {

        if (isValid == 1) {
            validateApplication(entity);
        }
        UserVO userVO;
        // 打印调试信息
        if (userContextDebugUtil != null) {
            userContextDebugUtil.printSimpleDebugInfo();
        }

        // 使用兼容的用户上下文服务，支持传统认证和OAuth2认证
        if (userContextService != null) {
            userVO = userContextService.getCurrentUser();

            // 如果无法获取用户信息，记录详细调试信息
            if (userVO == null) {
                log.warn("无法获取当前用户信息，可能是认证状态异常");
                if (userContextDebugUtil != null) {
                    userContextDebugUtil.printFullDebugInfo();
                }
            } else {
                log.info("获取到用户信息: userId={}, realName={}, authType={}", userVO.getId(), userVO.getRealName(), userVO.getId().startsWith("oauth2_") ? "OAuth2" : "Traditional");
            }
        } else {
            log.error("UserContextService 未注入，无法获取用户信息");
            userVO = SecurityUtils.getAccount();
        }


        // <editor-fold desc="新增">
        if (operType == 1) {
            // 处理第三方接口
            if (userVO != null) {
                entity.setDepartmentId(userVO.getDepartmentId());
            }
            DataUtils.setSaveFields(entity, userVO);
            entity.setDepartmentId(entity.getTownCode());
            entity.setFamilyCode("F" + generator.getUID());
            entity.setBatchNo(DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"));
        }
        // </editor-fold>

        // <editor-fold desc="更新">
        if (operType == 2 && isValid == 1) {
            PublicFamilyinfo publicFamilyinfo = this.selectOne(entity.getId());
            if (publicFamilyinfo.getAuditStatus() != 0 && publicFamilyinfo.getAuditStatus() != -1) {
                throw new BusinessException("操作失败，救助申请已受理结束");
            }

            if (entity.getAuditStatus() == 1) {
                log.warn("进入核对状态检查，AuditStatus = {}", entity.getAuditStatus());

                List<MsgsCheckDetail> msgsCheckDetailList = checkDetailService.selectListByFamilyid(publicFamilyinfo.getId());

                if (msgsCheckDetailList != null && !msgsCheckDetailList.isEmpty()) {
                    log.warn("核对记录列表：{}", JSON.toJSONString(msgsCheckDetailList));

                    boolean hasPendingCheck = msgsCheckDetailList.stream().anyMatch(d -> (d.getStatus() == 0 || d.getStatus() == 1) && d.getDeleteMark() == 0);

                    boolean hasCompletedCheck = msgsCheckDetailList.stream().anyMatch(d -> d.getStatus() == 4 && d.getDeleteMark() == 0);

                    // 如果存在正在核对，且没有完成核对记录，拒绝受理
                    if (hasPendingCheck && !hasCompletedCheck) {
                        log.warn("存在正在核对中的记录，受理拦截！");
                        throw new BusinessException("受理失败，正在等待核对反馈核对结果!");
                    }
                }
            }
        }
        // </editor-fold>

        // <editor-fold desc="检查数据完整性">

        List<String> errorAllList = new ArrayList<>(BeanValidatorUtil.validateAndGetMessages(entity));
        for (PublicFamilyMemberinfo member : entity.getPublicFamilyMemberinfoList()) {
            List<String> errorList = new ArrayList<>(BeanValidatorUtil.validateAndGetMessages(member));
            if (CollectionUtils.isNotEmpty(errorList)) {
                errorAllList.addAll(errorList);
                continue;
            }
            member.setIsPerfect(1);
        }
        for (PublicSurveyAgent agent : entity.getPublicSurveyAgentList()) {
            List<String> errorList = new ArrayList<>(BeanValidatorUtil.validateAndGetMessages(agent));
            if (CollectionUtils.isNotEmpty(errorList)) {
                errorAllList.addAll(errorList);
                continue;
            }
            agent.setIsPerfect(1);
        }

        if (CollectionUtils.isEmpty(entity.getPublicAttachmentinfoList())) {
            errorAllList.add("附件不能为空");
        }

        if (CollectionUtils.isNotEmpty(errorAllList)) {
            entity.setIsPerfect(0);
        } else {
            entity.setIsPerfect(1);
        }

        // </editor-fold>

        // <editor-fold desc="基础信息">

        // 设置家庭人数
        List<PublicFamilyMemberinfo> publicFamilyMemberinfoList = entity.getPublicFamilyMemberinfoList();
        int memberCount = CollectionUtil.size(publicFamilyMemberinfoList);
        entity.setPersonCount(String.valueOf(memberCount));
        entity.setPersonCountOnFlow(String.valueOf(memberCount));
        DataUtils.setUpdateFields(entity, userVO);

        // 拼接救助类型字符串（使用 StringBuilder，提升性能）
        entity.setFamilyCategoryVal("");
        entity.setFamilyCategoryKey("");
        List<PublicFamilyDistribution> distributionList;
        if (StringUtils.isNotEmpty(entity.getId()) && entity.getAuditStatus() == 1) {
            distributionList = distributionService.selectByFamilyId(entity.getId());
        } else {
            distributionList = entity.getPublicFamilyDistributionList();
        }
        if (CollectionUtil.isNotEmpty(distributionList)) {
            StringBuilder categoryValBuilder = new StringBuilder();
            StringBuilder categoryKeyBuilder = new StringBuilder();
            for (PublicFamilyDistribution distribution : distributionList) {
                categoryValBuilder.append(distribution.getFamilyCategoryVal()).append(",");
                categoryKeyBuilder.append(distribution.getFamilyCategoryKey()).append(",");
            }
            // 移除末尾逗号
            entity.setFamilyCategoryVal(StringUtils.removeEnd(categoryValBuilder.toString(), ","));
            entity.setFamilyCategoryKey(StringUtils.removeEnd(categoryKeyBuilder.toString(), ","));
        }
        // </editor-fold>

        if (operType == 1) {
            this.save(entity);
        }
        if (operType == 2) {
            this.update(entity);
        }

        //保存家庭成员人信息
        List<PublicFamilyMemberinfo> oldMemberInfoList = operType == 1 ? null : publicMemberService.selectByFamilyId(entity.getId());
        List<PublicFamilyMemberinfo> newMemberInfoList = entity.getPublicFamilyMemberinfoList();

        DataUtils.processDiff(oldMemberInfoList, newMemberInfoList, (insertList) -> {
            insertList.forEach(m -> {
                m.setPersonCode("P" + generator.getUID());
                m.setFamilyId(entity.getId());
            });
            publicMemberService.insertBatch(insertList);
        }, (updateList) -> {
            publicMemberService.updateBatch(updateList, 1000);
        }, (uw, deleteList, ids) -> {
            publicMemberService.update(uw);
            // 同步删除附件
            UpdateWrapper<PublicAttachmentinfo> attachmentDeleteWrapper = DataUtils.logicDeleteWrapper(PublicAttachmentinfo.class, userVO);
            attachmentDeleteWrapper.in("MEMBER_ID", ids);
            attachmentService.update(attachmentDeleteWrapper);
        }, userVO);

        Map<String, PublicFamilyMemberinfo> idCardToMemberMap = publicMemberService.selectFieldByFamilyId(entity.getId()).stream().filter(m -> StringUtils.isNotBlank(m.getPersonIdCard())).collect(Collectors.toMap(PublicFamilyMemberinfo::getPersonIdCard,  // key: 身份证号
                Function.identity(),                      // value: 原始对象
                (a, b) -> a                                // 处理 key 冲突时，保留第一个
        ));

        // 保存委托代理人/法定代理人信息
        List<PublicSurveyAgent> oldAgentList = operType == 1 ? null : agentService.selectByFamilyId(entity.getId());
        List<PublicSurveyAgent> newAgentList = new ArrayList<>();
        if (saveAgentMethod == 1) {
            newAgentList = entity.getPublicSurveyAgentList();
        }
        if (saveAgentMethod == 2) {
            for (PublicFamilyMemberinfo member : newMemberInfoList) {
                // 处理限制/无民事行为能力人员
                if (!Integer.valueOf(1).equals(member.getIsLimCapacity())) {
                    continue;
                }
                PublicSurveyAgent agent = member.getAgent();
                if (agent == null) {
                    throw new BusinessException("限制/无民事行为能力成员必须提供代理人信息");
                }
                // 主动绑定一下
                agent.setProxyName(member.getPersonName());
                agent.setProxyIdCard(member.getPersonIdCard());
                newAgentList.add(agent);
            }
        }
        DataUtils.processDiff(oldAgentList, newAgentList, (insertList) -> {
            insertList.forEach(m -> {
                m.setFamilyId(entity.getId());
                PublicFamilyMemberinfo member = idCardToMemberMap.get(m.getProxyIdCard());
                if (member == null) {
                    throw new BusinessException("未知的被代理人信息");
                }
                m.setMemberId(member.getId());
                m.setProxyName(member.getPersonName());
                m.setProxyIdCard(member.getPersonIdCard());
            });
            agentService.insertBatch(insertList);
        }, (updateList) -> {
            updateList.forEach(m -> {
                PublicFamilyMemberinfo member = idCardToMemberMap.get(m.getProxyIdCard());
                if (member == null) {
                    throw new BusinessException("未知的被代理人信息");
                }
                m.setMemberId(member.getId());
                m.setProxyName(member.getPersonName());
                m.setProxyIdCard(member.getPersonIdCard());
            });
            agentService.updateBatch(updateList, 1000);
        }, (uw, deleteList, ids) -> {
            agentService.update(uw);
        }, userVO);

        // 保存赡养抚养人信息
        List<PublicProviderinfo> oldProviderList = operType == 1 ? null : publicProviderService.selectByFamilyId(entity.getId());
        List<PublicProviderinfo> newProviderList = entity.getMsgsProviderinfoList();
        DataUtils.processDiff(oldProviderList, newProviderList, (insertList) -> {
            insertList.forEach(m -> {
                m.setFamilyId(entity.getId());
                PublicFamilyMemberinfo member = idCardToMemberMap.get(m.getMemberId());
                if (member != null) {
                    m.setMemberId(member.getId());
                }
            });
            publicProviderService.insertBatch(insertList);
        }, (updateList) -> {
            publicProviderService.updateBatch(updateList, 1000);
        }, (uw, deleteList, ids) -> {
            publicProviderService.update(uw);
        }, userVO);

        //救助类型
        List<PublicFamilyDistribution> oldDistributionList = operType == 1 ? null : distributionService.selectByFamilyId(entity.getId());
        List<PublicFamilyDistribution> newDistributionList = entity.getPublicFamilyDistributionList();
        DataUtils.processDiff(oldDistributionList, newDistributionList, (insertList) -> {
            insertList.forEach(m -> {
                m.setFamilyId(entity.getId());
            });
            distributionService.insertBatch(insertList);
        }, (updateList) -> {
            distributionService.updateBatch(updateList, 1000);
        }, (uw, deleteList, ids) -> {
            distributionService.update(uw);
        }, userVO);

        //保存家庭附件
        List<PublicAttachmentinfo> oldAttachList = operType == 1 ? null : attachmentService.selectByFamilyId(entity.getId());

        // 从家庭成员中提取所有附件
        List<PublicAttachmentinfo> newMemberAttachList = Optional.ofNullable(entity.getPublicFamilyMemberinfoList()).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).flatMap(member -> Optional.ofNullable(member.getAttachmentinfoList()).orElse(Collections.emptyList()).stream()).filter(Objects::nonNull).collect(Collectors.toList());

        // 家庭附件+成员附件
        List<PublicAttachmentinfo> newAttachList = entity.getPublicAttachmentinfoList().stream().filter(t -> StringUtils.isBlank(t.getMemberId())).collect(Collectors.toList());
        newAttachList.addAll(newMemberAttachList);

        DataUtils.processDiff(oldAttachList, newAttachList, (insertList) -> {
            insertList.forEach(m -> {
                m.setObjectId(entity.getId());
                PublicFamilyMemberinfo member = idCardToMemberMap.get(m.getMemberId());
                if (member != null) {
                    m.setMemberId(member.getId());
                }
            });
            attachmentService.insertBatch(insertList);
        }, (updateList) -> {
            attachmentService.updateBatch(updateList, 1000);
        }, (uw, deleteList, ids) -> {
            attachmentService.update(uw);
        }, userVO);

        //是否是审核提交
        if (entity.getAuditStatus() == 1) {
            try {
                pushMsgsFamilyinfo(entity);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    // </editor-fold>

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setAuditStatus(String id, int status) {
        //暂时只允许设置 为 0 ，设置其他状态时 需要补充相关校验逻辑
        if (status != 0) {
            throw new BusinessException("暂只支持设置为待受理状态");
        }
        this.update(DataUtils.setUpdateFields(this.buildUpdateWrapper()).eq("id", id).set("AUDIT_STATUS", status));
    }

    //同步到低保 ,临时救助
    @Transactional
    public void pushMsgsFamilyinfo(PublicFamilyinfo enity) throws Exception {
        UserVO userVO = SecurityUtils.getAccount();
        Date now = new Date();
        enity.setHandleDate(now);
        enity.setHandleUser(userVO.getRealName());
        this.save(enity);
        List<PublicFamilyDistribution> publicFamilyDistributionList = enity.getPublicFamilyDistributionList();

        log.info("----------------------保存临时救助2013-------------------");
        log.info(JSON.toJSONString(publicFamilyDistributionList));
        for (PublicFamilyDistribution publicFamilyDistribution : publicFamilyDistributionList) {
            //同步到低保特困，低边，支出型 其他低收入
            if ("01".equals(publicFamilyDistribution.getFamilyCategoryVal()) || "02".equals(publicFamilyDistribution.getFamilyCategoryVal()) || "03".equals(publicFamilyDistribution.getFamilyCategoryVal()) || "04".equals(publicFamilyDistribution.getFamilyCategoryVal()) || "05".equals(publicFamilyDistribution.getFamilyCategoryVal()) || "06".equals(publicFamilyDistribution.getFamilyCategoryVal())) {
                String familyId = syncDb(enity, publicFamilyDistribution);
                //更新转办信息
                publicFamilyDistribution.setBusId(familyId);
                distributionService.save(publicFamilyDistribution);
                //break;
            } else if ("21".equals(publicFamilyDistribution.getFamilyCategoryVal())) {//同步倒临时救助
                log.info("----------------------保存临时救助2023-------------------");
                String busId = tassFamilyInfoService.syncPublicFamilyApply(enity);

                //更新转办信息
                publicFamilyDistribution.setBusId(busId);
                distributionService.save(publicFamilyDistribution);
            } else {//专项救助
                saFeedbackService.synData(enity, publicFamilyDistribution);
            }

        }
    }

    @Transactional
    public String syncDb(PublicFamilyinfo enity, PublicFamilyDistribution publicFamilyDistribution) throws Exception {
        UserVO userVO = SecurityUtils.getAccount();
        Date now = new Date();
        MsgsFamilyinfo familyinfo = new MsgsFamilyinfo();
        familyinfo = CommonUtil.CopyObj(enity, familyinfo);
        familyinfo.setDomicileAdress(enity.getCensusRegister());
        familyinfo.setResidenceAddress(enity.getFamilyAddress());
        familyinfo.setFamilyCategoryKey(publicFamilyDistribution.getFamilyCategoryKey());
        familyinfo.setFamilyCategoryVal(publicFamilyDistribution.getFamilyCategoryVal());
        familyinfo.setFamilySubcategoryVal("");
        familyinfo.setFamilySubcategoryKey("");
        familyinfo.setDeleteMark(0);//是否删除
        familyinfo.setCreateDate(now);
        familyinfo.setCreateUserId(userVO.getId());
        familyinfo.setCreateUserName(userVO.getRealName());
        familyinfo.setCreateOrgCode(userVO.getDepartmentId());
        familyinfo.setCreateOrgName(userVO.getDepartmentName());
        familyinfo.setPersonCount(enity.getPublicFamilyMemberinfoList().size());//总人数
        familyinfo.setPersonCountOnFlow(enity.getPublicFamilyMemberinfoList().size());//保障人口数
        familyinfo.setTotalFund(new BigDecimal(0));// 保障金额（汇总成员表）
        familyinfo.setReliefFund(new BigDecimal(0));// 差额救助总金额（汇总成员表）
        familyinfo.setClassifyFund(new BigDecimal(0)); // 分类施保总金额（汇总成员表）
        familyinfo.setOperationCategory(0);//新申请
        familyinfo.setDataStatus(0);//数据状态草稿
        familyinfo.setDataSource(2);//数据来源为自主申请
        familyinfo.setFlowStatus(0);//流程状态操作
        familyinfo.setIsPerfect(0);//是否完善
        familyinfo.setEffectiveStatus(0);//生效状态 0 已生效 1 等待生效 2 等待失效
        familyinfo.setModifyDate(now);
        familyinfo.setCheckState(0);//核对状态 0 未发起核对 1 已发起核对 2 核对成功 3 核对不通过
        familyinfo.setReviewStatus(0);// 复核状态：0：正常数据；1：正在被复查；2：正在停发；3：正在注销；4：正在恢复；5：正在被迁移；
        familyinfo.setAuditStatus(0);//审核状态 0待审核 1审核通过 2审核驳回
        familyInfoService.insert(familyinfo);
        String familyId = familyinfo.getId();
        String familyCode = familyinfo.getFamilyCode();
        log.info("---------------------------------------------------------");
        log.info(JSON.toJSONString(familyinfo));
        List<MsgsFamilymemberinfo> memberList = enity.getPublicFamilyMemberinfoList()
                .stream()
                .map(src -> {
                    MsgsFamilymemberinfo t = new MsgsFamilymemberinfo();
                    BeanUtils.copyProperties(src, t);          // 基本拷贝
                    t.setId(null);                             // 新主键
                    t.setFamilyCode(familyCode);
                    t.setFamilyId(familyId);
                    t.setIsPerfect(0);
                    t.setDeleteMark(0);
                    t.setNationalityVal(enity.getNationalityVal());
                    t.setNationalityKey(enity.getNationalityKey());
                    t.setIsIndemnifyPerson(1);
                    t.setIncome(BigDecimal.ZERO);
                    t.setTotalFund(BigDecimal.ZERO);
                    t.setReliefFund(BigDecimal.ZERO);
                    t.setClassifyFund(BigDecimal.ZERO);
                    t.setExpenditure(BigDecimal.ZERO);
                    t.setSex(IdCardUtils.extractSex(t.getPersonIdCard()));
                    t.setBirthday(IdCardUtils.extractBirthday(t.getPersonIdCard()));
                    t.setIsCivilCapacity(src.getIsLimCapacity());
                    return t;
                })
                .collect(Collectors.toList());
        memberService.insertBatch(memberList);
        Map<String, String> mapMemberId = IntStream.range(0, enity.getPublicFamilyMemberinfoList().size())
                .boxed()
                .collect(Collectors.toMap(
                        i -> enity.getPublicFamilyMemberinfoList().get(i).getId(),
                        i -> memberList.get(i).getId()));

        /* 3. 委托人 批量 */
        if (enity.getPublicSurveyAgentList() != null && enity.getPublicSurveyAgentList().size() > 0) {
            List<MsgsSurveyAgent> agentList = enity.getPublicSurveyAgentList()
                    .stream()
                    .map(src -> {
                        MsgsSurveyAgent t = new MsgsSurveyAgent();
                        BeanUtils.copyProperties(src, t);
                        t.setId(null);
                        t.setFamilyId(familyId);
                        t.setMemberId(mapMemberId.get(src.getMemberId()));
                        return t;
                    })
                    .collect(Collectors.toList());
            msgsSurveyAgentService.insertBatch(agentList);
        }

        /* 4. 赡养人 批量 */
        if (enity.getMsgsProviderinfoList() != null && enity.getMsgsProviderinfoList().size() > 0) {
            List<MsgsProviderinfo> proList = enity.getMsgsProviderinfoList()
                    .stream()
                    .map(src -> {
                        MsgsProviderinfo t = new MsgsProviderinfo();
                        BeanUtils.copyProperties(src, t);
                        t.setId(null);
                        t.setMemberId(mapMemberId.get(src.getMemberId()));
                        t.setFamilyId(familyId);
                        return t;
                    })
                    .collect(Collectors.toList());
            providerService.insertBatch(proList);
        }

        /* 5. 附件 批量 */
        if (enity.getPublicAttachmentinfoList() != null && enity.getPublicAttachmentinfoList().size() > 0) {
            List<MsgsAttachmentinfo> attList = enity.getPublicAttachmentinfoList()
                    .stream()
                    .map(src -> {
                        MsgsAttachmentinfo t = new MsgsAttachmentinfo();
                        BeanUtils.copyProperties(src, t, "id");
                        t.setMemberId(mapMemberId.get(src.getMemberId()));
                        t.setObjectId(familyId);
                        t.setObjectType(0);
                        return t;
                    })
                    .collect(Collectors.toList());
            attachmentInfoService.insertBatch(attList);
        }

        /* 6. 核查明细 批量 */
        List<MsgsCheckDetail> details = checkDetailService.selectListByFamilyid(enity.getId())
                .stream()
                .peek(d -> {
                    d.setId(null);
                    d.setBatchId(null);
                    d.setFamilyId(familyId);
                })
                .collect(Collectors.toList());
        checkDetailService.insertBatch(details);

        return familyinfo.getId();
    }

    //救助认定-临时救助转低保
    @Transactional
    public String tassSyncDb(TransferFamilyinfoVO entity, PublicFamilyDistribution publicFamilyDistribution) throws Exception {
        UserVO userVO = SecurityUtils.getAccount();
        Date now = new Date();
        TassFamilyInfo tassFamilyInfo = tassFamilyInfoService.selectOne(entity.getId());
        if (tassFamilyInfo == null) {
            throw new IllegalArgumentException("未查询到家庭信息！");
        }
        MsgsFamilyinfo familyinfo = new MsgsFamilyinfo();
        familyinfo = CommonUtil.CopyObj(tassFamilyInfo, familyinfo);
        familyinfo.setFamilyCategoryKey(publicFamilyDistribution.getFamilyCategoryKey());
        familyinfo.setFamilyCategoryVal(publicFamilyDistribution.getFamilyCategoryVal());
        familyinfo.setFamilySubcategoryVal("");
        familyinfo.setFamilySubcategoryKey("");
        familyinfo.setDeleteMark(0);//是否删除
        familyinfo.setCreateDate(now);
        familyinfo.setCreateUserId(userVO.getId());
        familyinfo.setCreateUserName(userVO.getRealName());
        familyinfo.setCreateOrgCode(userVO.getDepartmentId());
        familyinfo.setCreateOrgName(userVO.getDepartmentName());
        familyinfo.setPersonCount(Converter.toInteger(tassFamilyInfo.getPersonCount()));//总人数
        familyinfo.setTotalFund(new BigDecimal(0));// 保障金额（汇总成员表）
        familyinfo.setReliefFund(new BigDecimal(0));// 差额救助总金额（汇总成员表）
        familyinfo.setClassifyFund(new BigDecimal(0)); // 分类施保总金额（汇总成员表）
        familyinfo.setOperationCategory(0);//新申请
        familyinfo.setDataStatus(0);//数据状态草稿
        familyinfo.setDataSource(1);//数据来源为受理登记
        familyinfo.setFlowStatus(0);//流程状态操作
        familyinfo.setIsPerfect(0);//是否完善
        familyinfo.setEffectiveStatus(0);//生效状态 0 已生效 1 等待生效 2 等待失效
        familyinfo.setModifyDate(now);
        familyinfo.setCheckState(0);//核对状态 0 未发起核对 1 已发起核对 2 核对成功 3 核对不通过
        familyinfo.setReviewStatus(0);// 复核状态：0：正常数据；1：正在被复查；2：正在停发；3：正在注销；4：正在恢复；5：正在被迁移；
        familyinfo.setAuditStatus(0);//审核状态 0待审核 1审核通过 2审核驳回
        familyInfoService.insert(familyinfo);

        Map<String, String> mapMemberId = new HashMap<String, String>();
        List<TassFamilyMemberInfo> tassFamilyMemberInfoList = tassFamilyMemberInfoService.selectList(new QueryWrapper<TassFamilyMemberInfo>().eq("DELETE_MARK", 0).eq("BUSINESS_NO", tassFamilyInfo.getBusinessNo()));
        List<MsgsFamilymemberinfo> msgsFamilymemberinfos = new ArrayList<>();
        for (TassFamilyMemberInfo tassFamilyMemberInfo : tassFamilyMemberInfoList) {
            MsgsFamilymemberinfo msgsFamilymemberinfo = new MsgsFamilymemberinfo();
            msgsFamilymemberinfo = CommonUtil.CopyObj(tassFamilyMemberInfo, msgsFamilymemberinfo);
            msgsFamilymemberinfo.setFamilyCode(familyinfo.getFamilyCode());
            msgsFamilymemberinfo.setIsPerfect(0);//未完善
            msgsFamilymemberinfo.setFamilyId(familyinfo.getId());//ID
            msgsFamilymemberinfo.setDeleteMark(0);
            msgsFamilymemberinfo.setIsIndemnifyPerson(1);//是否保障对象
            msgsFamilymemberinfo.setIncome(new BigDecimal(0));//收入
            msgsFamilymemberinfo.setTotalFund(new BigDecimal(0));// 保障金额
            msgsFamilymemberinfo.setReliefFund(new BigDecimal(0));// 差额救助总金额
            msgsFamilymemberinfo.setClassifyFund(new BigDecimal(0)); // 分类施保总金额
            msgsFamilymemberinfo.setExpenditure(new BigDecimal(0)); // 支出
            msgsFamilymemberinfo.setId("");
            msgsFamilymemberinfos.add(msgsFamilymemberinfo);
        }
        memberService.insertBatch(msgsFamilymemberinfos);

        List<TassAttachmentInfo> attachmentList = tassAttachmentInfoService.selectList(new QueryWrapper<TassAttachmentInfo>().eq("DELETE_MARK", 0).eq("BUSINESS_NO", tassFamilyInfo.getBusinessNo()));
        List<MsgsAttachmentinfo> attachmentInfos = new ArrayList<>();
        for (TassAttachmentInfo tassAttachmentInfo : attachmentList) {
            MsgsAttachmentinfo attachmentInfo = new MsgsAttachmentinfo();
            attachmentInfo = CommonUtil.CopyObj(tassAttachmentInfo, attachmentInfo);
            attachmentInfo.setMemberId(mapMemberId.get(tassAttachmentInfo.getMemberId()));
            attachmentInfo.setObjectId(familyinfo.getId());//ID
            attachmentInfos.add(attachmentInfo);
        }
        attachmentInfoService.insertBatch(attachmentInfos);
        return familyinfo.getId();
    }

    //更新协同结果
    @Transactional
    @Override
    public void updateCooperative(CooperativeVO entity) {
        UserVO userVO = SecurityUtils.getAccount();
        PublicFamilyDistribution familyDistribution = distributionService.selectOne(new QueryWrapper<PublicFamilyDistribution>().eq("bus_id", entity.getBusId()));
        if (familyDistribution != null) {
            familyDistribution.setStatus(entity.getStatus());
            familyDistribution.setReliefFund(entity.getReliefFund());
            familyDistribution.setCooperativeContent(entity.getCooperativeContent());
            familyDistribution.setCheckOpinion(entity.getCheckOpinion());
            familyDistribution.setModifyDate(new Date());
            familyDistribution.setModifyUserId(userVO.getId());
            familyDistribution.setModifyUserName(userVO.getRealName());
            distributionService.update(familyDistribution);

            List<PublicFamilyDistribution> publicFamilyDistributions = distributionService.selectList(new QueryWrapper<PublicFamilyDistribution>().eq("FAMILY_ID", familyDistribution.getFamilyId()).eq("DELETE_MARK", 0));

            for (PublicFamilyDistribution pub : publicFamilyDistributions) {
                if (pub.getStatus() == 0) {//有状态为0的，说明还有事项未办完
                    return;
                }
            }
            //所有协同事项已办，更新受理家庭表状态为办结
            this.update(new UpdateWrapper<PublicFamilyinfo>().eq("id", familyDistribution.getFamilyId()).set("AUDIT_STATUS", 10).set("DONE_DATE", new Date()).set("MODIFY_USER_NAME", userVO.getRealName()).set("MODIFY_USER_ID", userVO.getId()));
        }
    }

    //更新协同结果
    @Transactional
    @Override
    public void updateSpecCooperative(CooperativeVO entity) {
        UserVO userVO = SecurityUtils.getAccount();
        PublicFamilyDistribution familyDistribution = distributionService.selectOne(new QueryWrapper<PublicFamilyDistribution>().eq("id", entity.getId()));
        familyDistribution.setStatus(entity.getStatus());
        familyDistribution.setReliefFund(entity.getReliefFund());
        familyDistribution.setCooperativeContent(entity.getCooperativeContent());
        familyDistribution.setCheckOpinion(entity.getCheckOpinion());
        familyDistribution.setModifyDate(new Date());
        familyDistribution.setModifyUserId(userVO.getId());
        familyDistribution.setModifyUserName(userVO.getRealName());
        distributionService.update(familyDistribution);

        List<PublicFamilyDistribution> publicFamilyDistributions = distributionService.selectList(new QueryWrapper<PublicFamilyDistribution>().eq("FAMILY_ID", familyDistribution.getFamilyId()).eq("DELETE_MARK", 0));

        for (PublicFamilyDistribution pub : publicFamilyDistributions) {
            if (pub.getStatus() == 0) {//有状态为0的，说明还有事项未办完
                return;
            }
        }
        //所有协同事项已办，更新受理家庭表状态为办结
        this.update(new UpdateWrapper<PublicFamilyinfo>().eq("id", familyDistribution.getFamilyId()).set("AUDIT_STATUS", 10));
    }

    @Override
    public void deleteDataById(String id) {
        UserVO userVO = SecurityUtils.getAccount();
        this.update(new UpdateWrapper<PublicFamilyinfo>().eq("ID", id).set("DELETE_MARK", 1).set("MODIFY_DATE", new Date()).set("MODIFY_USER_ID", userVO.getId()).set("MODIFY_USER_NAME", userVO.getRealName()));
    }

    @Override
    public void restoreDataById(String id) {
        UserVO userVO = SecurityUtils.getAccount();
        this.update(new UpdateWrapper<PublicFamilyinfo>().eq("ID", id).set("DELETE_MARK", 0).set("MODIFY_DATE", new Date()).set("MODIFY_USER_ID", userVO.getId()).set("MODIFY_USER_NAME", userVO.getRealName()));
    }

    @Override
    public List<PublicFamilyinfoVO> selectFamilyinfoByIds(List<String> ids) {
        return this.baseMapper.selectFamilyinfoByIds(ids);
    }

    // <editor-fold desc="转办">
    @Override
    @Transactional
    public String transferData(TransferFamilyinfoVO entity) throws Exception {
        //根据业务id 获取转办信息
        PublicFamilyDistribution publicFamilyDistribution = distributionService.selectOne(new QueryWrapper<PublicFamilyDistribution>().eq("DELETE_MARK", 0).eq("BUS_ID", entity.getId()));
        if (publicFamilyDistribution != null) {//综合受理的救助认定-转救助
            return saveAccept(entity, publicFamilyDistribution);
        } else {//救助认定新登记-转救助
            return saveAssistance(entity);
        }

    }

    //受理转办
    public String saveAccept(TransferFamilyinfoVO entity, PublicFamilyDistribution publicFamilyDistribution) throws Exception {
        Date now = new Date();
        UserVO userVO = SecurityUtils.getAccount();
        String db = "01,02,03,04,05,06";
        List<PublicFamilyDistribution> publicFamilyDistributions = distributionService.selectList(new QueryWrapper<PublicFamilyDistribution>().eq("DELETE_MARK", 0).eq("FAMILY_ID", publicFamilyDistribution.getFamilyId()).ne("FAMILY_CATEGORY_VAL", entity.getFamilyCategoryVal()));
        //判断之前转办类型与现在选择类型是否冲突
        if (publicFamilyDistributions.size() > 0) {
            for (PublicFamilyDistribution familyDistribution : entity.getFamilyDistributionList()) {
                for (PublicFamilyDistribution distribution : publicFamilyDistributions) {
                    if (distribution.getFamilyCategoryVal().equals(familyDistribution.getFamilyCategoryVal())) {
                        return "该对象已经是" + distribution.getFamilyCategoryKey();
                    }
                }
            }
        }

        //删除之前的转办信息
        publicFamilyDistribution.setDeleteMark(1);
        distributionService.save(publicFamilyDistribution);

        if (entity.getFamilyCategoryVal().equals("21")) {//临时救助
            tassFamilyInfoService.update(new UpdateWrapper<TassFamilyInfo>().set("DELETE_MARK", 1).eq("id", entity.getId()));
            for (PublicFamilyDistribution familyDistribution : entity.getFamilyDistributionList()) {
                //新增转办信息
                PublicFamilyDistribution newFamilyDistribution = new PublicFamilyDistribution();
                BeanUtils.copyProperties(familyDistribution, newFamilyDistribution);
                newFamilyDistribution.setId("");
                newFamilyDistribution.setFamilyId(publicFamilyDistribution.getFamilyId());
                newFamilyDistribution.setDeleteMark(0);//是否删除
                newFamilyDistribution.setCreateDate(now);
                newFamilyDistribution.setCreateUserId(userVO.getId());
                newFamilyDistribution.setCreateUserName(userVO.getRealName());
                if (db.contains(entity.getFamilyCategoryVal())) {//低保
                    PublicFamilyinfo publicFamilyinfo = this.selectOne(entity.getId());
                    syncDb(publicFamilyinfo, newFamilyDistribution);
                    newFamilyDistribution.setBusId(entity.getId());
                }
                distributionService.save(newFamilyDistribution);
                //转到专项救助的
                if (Converter.toInteger(newFamilyDistribution.getFamilyCategoryVal()) > 21) {
                    PublicFamilyinfo publicFamilyinfo = this.selectOne(newFamilyDistribution.getFamilyId());
                    saFeedbackService.synData(publicFamilyinfo, newFamilyDistribution);
                }
            }
        } else if (db.contains(entity.getFamilyCategoryVal())) {//低保
            Boolean bl = true;
            for (PublicFamilyDistribution familyDistribution : entity.getFamilyDistributionList()) {
                //新增转办信息
                PublicFamilyDistribution newFamilyDistribution = new PublicFamilyDistribution();
                BeanUtils.copyProperties(familyDistribution, newFamilyDistribution);
                newFamilyDistribution.setId("");
                newFamilyDistribution.setFamilyId(publicFamilyDistribution.getFamilyId());
                newFamilyDistribution.setDeleteMark(0);//是否删除
                newFamilyDistribution.setCreateDate(now);
                newFamilyDistribution.setCreateUserId(userVO.getId());
                newFamilyDistribution.setCreateUserName(userVO.getRealName());
                if (familyDistribution.getFamilyCategoryVal().equals("21")) {//临时救助
                    String busId = tassFamilyInfoService.syncDbFamilyApply(entity.getId(), 2);//2自助申请-受理
                    newFamilyDistribution.setBusId(busId);
                } else if (db.contains(familyDistribution.getFamilyCategoryVal())) {//低保
                    newFamilyDistribution.setBusId(entity.getId());
                    familyInfoService.update(new UpdateWrapper<MsgsFamilyinfo>().set("FAMILY_CATEGORY_VAL", familyDistribution.getFamilyCategoryVal()).set("FAMILY_CATEGORY_KEY", familyDistribution.getFamilyCategoryKey()).set("FAMILY_SUBCATEGORY_VAL", "").set("FAMILY_SUBCATEGORY_KEY", "").set("IS_PERFECT", 0).eq("id", entity.getId()));
                    memberService.update(new UpdateWrapper<MsgsFamilymemberinfo>().set("IS_PERFECT", 0).eq("FAMILY_ID", entity.getId()));
                    bl = false;
                }
                distributionService.insert(newFamilyDistribution);
                //转到专项救助的
                if (Converter.toInteger(newFamilyDistribution.getFamilyCategoryVal()) > 21) {
                    PublicFamilyinfo publicFamilyinfo = this.selectOne(newFamilyDistribution.getFamilyId());
                    saFeedbackService.synData(publicFamilyinfo, newFamilyDistribution);
                }
            }
            if (bl) {
                familyInfoService.update(new UpdateWrapper<MsgsFamilyinfo>().set("DELETE_MARK", 1).eq("id", entity.getId()));
            }
        }
        return "";
    }

    //救助认定登记的转办
    public String saveAssistance(TransferFamilyinfoVO entity) throws Exception {
        String db = "01,02,03,04,05,06";
        if (entity.getFamilyCategoryVal().equals("21")) {//临时救助
            tassFamilyInfoService.update(new UpdateWrapper<TassFamilyInfo>().set("DELETE_MARK", 1).eq("id", entity.getId()));
            for (PublicFamilyDistribution familyDistribution : entity.getFamilyDistributionList()) {//临时救助只能转低保
                tassSyncDb(entity, familyDistribution);
            }
        } else if (db.contains(entity.getFamilyCategoryVal())) {//低保
            Boolean bl = true;
            for (PublicFamilyDistribution familyDistribution : entity.getFamilyDistributionList()) {
                //新增转办信息
                if (familyDistribution.getFamilyCategoryVal().equals("21")) {//临时救助
                    tassFamilyInfoService.syncDbFamilyApply(entity.getId(), 1);//救助认定的申请类型
                } else if (db.contains(familyDistribution.getFamilyCategoryVal())) {//低保
                    familyInfoService.update(new UpdateWrapper<MsgsFamilyinfo>().set("FAMILY_CATEGORY_VAL", familyDistribution.getFamilyCategoryVal()).set("FAMILY_CATEGORY_KEY", familyDistribution.getFamilyCategoryKey()).set("FAMILY_SUBCATEGORY_VAL", "").set("FAMILY_SUBCATEGORY_KEY", "").set("IS_PERFECT", 0).eq("id", entity.getId()));
                    memberService.update(new UpdateWrapper<MsgsFamilymemberinfo>().set("IS_PERFECT", 0).eq("FAMILY_ID", entity.getId()));
                    bl = false;
                }
            }
            if (bl) {
                familyInfoService.update(new UpdateWrapper<MsgsFamilyinfo>().set("DELETE_MARK", 1).eq("id", entity.getId()));
            }
        }
        return "";
    }
    // </editor-fold>

    // <editor-fold desc="授权书">

    /**
     * 授权书
     */
    public ByteArrayOutputStream previewAuthLetter(String id, List<MemberCheckAuthDto> authList, boolean isAuthNumber) {
        PublicFamilyinfo entity = this.selectOne(id);
        if (entity == null) {
            return null;
        }
        List<MemberAuthDto> memberAuthList = new ArrayList<>();
        List<AgentAuthDto> agentAuthList = new ArrayList<>();
        // 绑定共同生活家庭成员
        List<PublicFamilyMemberinfo> members = publicMemberService.querier().eq("FAMILY_ID", entity.getId()).eq("DELETE_MARK", 0).list();
        // 委托代理人/法定代理人信息
        List<PublicSurveyAgent> agents = agentService.querier().eq("FAMILY_ID", entity.getId()).eq("DELETE_MARK", 0).list();
        for (PublicFamilyMemberinfo member : members) {
            MemberAuthDto authDto = new MemberAuthDto();
            BeanUtils.copyProperties(member, authDto);
            authDto.setName(member.getPersonName());
            authDto.setIdCard(member.getPersonIdCard());
            authDto.setRelationApplicantVal(member.getFamilyRelationVal());
            authDto.setRelationApplicantKey(member.getFamilyRelationKey());
            agents.stream().filter(t -> t.getMemberId().equals(member.getId())).findFirst().ifPresent(t -> {
                authDto.addExtendInfo("agentName", t.getName());
            });
            memberAuthList.add(authDto);
        }
        for (PublicSurveyAgent agent : agents) {
            AgentAuthDto authDto = new AgentAuthDto();
            BeanUtils.copyProperties(agent, authDto);
            agentAuthList.add(authDto);
        }
        String templateName = "household/survey/青海省居民家庭经济状况核对授权书模板.docx";
        return memberCheckAuthService.generateAuthLetter(templateName, authList, memberAuthList, agentAuthList, isAuthNumber);
    }

    @Override
    public boolean isCheckAuth() {
        if (projectProperties == null) {
            return true;
        }
        ProjectProperties.PublicFamilyInfo familyInfo = projectProperties.getPublicFamilyInfo();
        if (familyInfo == null) {
            return true;
        }
        ProjectProperties.MemberCheckAuth auth = familyInfo.getMemberCheckAuth();
        if (auth == null) {
            return true;
        }
        return auth.isEnable(); // 默认为 false
    }
    // </editor-fold>

    // <editor-fold desc="告知承诺书">

    /**
     * 告知承诺书
     */
    @Override
    public ByteArrayOutputStream generateAssistanceCommitmentLetter(String id) {
        PublicFamilyinfo entity = this.selectOne(id);
        if (entity == null) {
            return null;
        }
        // 绑定家庭
        AssistanceApplicationDto applicationDto = new AssistanceApplicationDto();
        BeanUtils.copyProperties(entity, applicationDto);
        applicationDto.setName(entity.getHouseholder());
        applicationDto.setIdCard(entity.getHouseholderIdCard());
        applicationDto.setTel(entity.getTel());
        String templateName = "household/survey/青海省社会救助申请证明事项告知承诺书模板.docx";
        return memberCheckAuthService.generateAssistanceCommitmentLetter(templateName, applicationDto);
    }
    // </editor-fold>

    // <editor-fold desc="上传授权书、承诺告知书">

    @Transactional
    @Override
    public void uploadLetter(String id) {
        PublicAttachmentinfo authLetterAttach = new PublicAttachmentinfo();
        authLetterAttach.setObjectId(id);
        authLetterAttach.setObjectType(0);
        authLetterAttach.setFileCategory(FileCategory.AUTHORIZATION_LETTER.getCode());
        authLetterAttach.setFileCategoryName(FileCategory.AUTHORIZATION_LETTER.getMsg());

        PublicAttachmentinfo acLetterAttach = new PublicAttachmentinfo();
        acLetterAttach.setObjectId(id);
        acLetterAttach.setObjectType(0);
        acLetterAttach.setFileCategory(FileCategory.OTHER.getCode());
        acLetterAttach.setFileCategoryName(FileCategory.OTHER.getMsg());
        try {
            memberCheckAuthService.docLetterToPngUpload(attachmentService, authLetterAttach, PublicAttachmentinfo::new, "核对授权书（自动生成）", previewAuthLetter(id, null, true));
            memberCheckAuthService.docLetterToPngUpload(attachmentService, acLetterAttach, PublicAttachmentinfo::new, "承诺告知书（自动生成）", generateAssistanceCommitmentLetter(id));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // </editor-fold>
}