package net.jqsoft.zhmz.cloudplatform.msgs.utils;

import org.apache.pdfbox.multipdf.PDFMergerUtility;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2025/7/22 18:37
 */
public class PdfMergerUtil {

    /**
     * 合并pdf
     *
     * @param pdfs
     * @return
     * @throws IOException
     */
    public static byte[] mergePdfs(List<byte[]> pdfs) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        PDFMergerUtility merger = new PDFMergerUtility();
        merger.setDestinationStream(output);

        for (byte[] pdf : pdfs) {
            try (InputStream is = new ByteArrayInputStream(pdf)) {
                merger.addSource(is);
            }
        }

        merger.mergeDocuments(null);
        return output.toByteArray();
    }
}
