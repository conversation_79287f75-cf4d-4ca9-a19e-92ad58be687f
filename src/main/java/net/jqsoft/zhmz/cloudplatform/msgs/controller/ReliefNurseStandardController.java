package net.jqsoft.zhmz.cloudplatform.msgs.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jqsoft.apis.controller.SuperController;
import net.jqsoft.apis.util.QueryParams;
import net.jqsoft.common.domain.DataRespond;
import net.jqsoft.common.domain.ListRespond;
import net.jqsoft.common.domain.MsgRespond;
import net.jqsoft.excel.innter.ExcelMate;
import net.jqsoft.excel.service.ExcelService;
import net.jqsoft.persist.mybatisplus.query.Page;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.ModuleType;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.OPerateType;
import net.jqsoft.zhmz.cloudplatform.msgs.manager.AsyncManager;
import net.jqsoft.zhmz.cloudplatform.msgs.manager.factory.AsyncFactory;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.ReliefNurseStandard;
import net.jqsoft.zhmz.cloudplatform.msgs.model.vo.StatusVO;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IReliefNurseStandardService;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.CommonUtil;
import net.jqsoft.zhmz.common.vo.UserVO;
import org.jsets.fastboot.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023-11-07 14:24:11
 */
@Slf4j
@RestController
@RequestMapping("/relief_nurse_standard")
@Api(tags = "护理补贴标准")
public class ReliefNurseStandardController extends SuperController<String, ReliefNurseStandard, IReliefNurseStandardService> {

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private ExcelService excelService;

    /**
     * 增加
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("增加护理补贴标准")
    public MsgRespond insert(@RequestBody @Validated ReliefNurseStandard entity) {
        long startTime = System.currentTimeMillis();
        if(this.service.isExist(entity)) {
            return MsgRespond.fail("添加失败，该地区的护理标准已存在");
        }
        UserVO account = SecurityUtils.getAccount();
        if (account != null) {
            entity.setCreateUserId(account.getId());
            entity.setCreateUserName(account.getUsername());
        }
        this.service.save(entity);
        //记录操作日志
        AsyncManager.me().execute(AsyncFactory.recordOper(
                ModuleType.RELIEF_NURSE_STANDARD, OPerateType.ADD,entity.getAreaName(),true,"新增成功",
                startTime, CommonUtil.getIpAddress(httpServletRequest),CommonUtil.getBrower(httpServletRequest)
        ));
        return this.successRespond(MSG_INSERT_SUCCEED);
    }

    /**
     * 删除
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("删除护理补贴标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", required = true, paramType = "path"),
    })
    public MsgRespond delete(@PathVariable("id") String id) {
        long startTime = System.currentTimeMillis();
        this.service.deleteById(id);
        //记录操作日志
        AsyncManager.me().execute(AsyncFactory.recordOper(
                ModuleType.RELIEF_NURSE_STANDARD, OPerateType.DELETE, id,true,"删除成功",
                startTime,CommonUtil.getIpAddress(httpServletRequest),CommonUtil.getBrower(httpServletRequest)
        ));
        return this.successRespond(MSG_DELETE_SUCCEED);
    }

    /**
     * 修改
     */
    @PutMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("修改护理补贴标准")
    public MsgRespond update(@RequestBody @Validated ReliefNurseStandard entity) {
        long startTime = System.currentTimeMillis();
        if(this.service.isExist(entity)) {
            return MsgRespond.fail("修改失败，该地区的护理标准已存在");
        }
        UserVO account = SecurityUtils.getAccount();
        if (account != null) {
            entity.setModifyUserId(account.getId());
            entity.setModifyUserName(account.getUsername());
        }
        this.service.update(entity);
        //记录操作日志
        AsyncManager.me().execute(AsyncFactory.recordOper(
                ModuleType.RELIEF_NURSE_STANDARD, OPerateType.UPDATE,entity.getId(),true,"修改成功",
                startTime,CommonUtil.getIpAddress(httpServletRequest),CommonUtil.getBrower(httpServletRequest)
        ));
        return this.successRespond(MSG_UPDATE_SUCCEED);
    }

    /**
     * 根据ID获取
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("获取护理补贴标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", required = true, paramType = "path"),
    })
    public DataRespond<ReliefNurseStandard> get(@PathVariable("id") String id) {
        return this.dataRespond(this.service.selectOne(id));
    }

    /**
     * 根据条件获取列表
     * 如果page_num和page_size都不为空进行分页查询否则进行列表查询
     */
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("查询护理补贴标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page_num", value = "当前页码", required = false, paramType = "query")
            , @ApiImplicitParam(name = "page_size", value = "每页条数", required = false, paramType = "query")
            , @ApiImplicitParam(name = "areaCode", value = "区划编码", required = false, paramType = "query")
            , @ApiImplicitParam(name = "status", value = "启用/停用", required = false, paramType = "query")
    })
    public ListRespond<ReliefNurseStandard> list(
            @RequestParam(name = "page_num", required = false) Integer pageNum
            , @RequestParam(name = "page_size", required = false) Integer pageSize
            , @RequestParam(name = "areaCode", required = false) String areaCode
            , @RequestParam(name = "status", required = false) String status
    ) {

        QueryParams params = QueryParams.build();
        params.add("areaCode", areaCode);
        params.add("status", status);

        if (null != pageNum && null != pageSize) { // 需要分页
            params.setPageNum(pageNum).setPageSize(pageSize);
            return this.pageRespond(this.service.selectReliefNurseStandardPage(params));
        }
        // 渲染列表数据的结果
        return this.listRespond(this.service.selectReliefNurseStandardList(params));
    }

    /**
     *  护理补贴标准-启用/停用
     */
    @PutMapping(value = "/enable", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("启用/停用护理补贴标准")
    public MsgRespond enable(@RequestBody @Validated StatusVO statusVO) {
        long startTime = System.currentTimeMillis();
        UserVO account = SecurityUtils.getAccount();
        if (account != null) {
            statusVO.setModifyUserId(account.getId());
            statusVO.setModifyUserName(account.getUsername());
            statusVO.setModifyDate(new Date());
        }
        this.service.enable(statusVO);
        //记录操作日志
        AsyncManager.me().execute(AsyncFactory.recordOper(
                ModuleType.RELIEF_NURSE_STANDARD, OPerateType.UPDATE,statusVO.getIds().toString(),true,"批量启用/停用成功",
                startTime,CommonUtil.getIpAddress(httpServletRequest),CommonUtil.getBrower(httpServletRequest)
        ));
        return this.successRespond(MSG_UPDATE_SUCCEED);
    }

    /**
     * Excel导入
     */
    @PostMapping("/excel_import")
    @ApiOperation("Excel导入")
    public MsgRespond excelImport(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null != file && file.getSize() > 0) {
            List<ReliefNurseStandard> list = this.excelService.doImport(ReliefNurseStandard.class, file);
            this.service.insertBatch(list);
            return this.successRespond();
        }
        return this.failRespond("文件不能为空");
    }

    /**
     * Excel导出
     */
    @GetMapping("/excel_export")
    @ApiOperation("Excel导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page_num", value = "当前页码", required = false, paramType = "query")
            , @ApiImplicitParam(name = "page_size", value = "每页条数", required = false, paramType = "query")
    })
    public void excelExport(
            @RequestParam(name = "page_num", required = false) Integer pageNum
            , @RequestParam(name = "page_size", required = false) Integer pageSize
            , HttpServletResponse response
    ) {

        QueryParams params = QueryParams.build();

        List<ReliefNurseStandard> datas = new ArrayList<ReliefNurseStandard>();
        if (null != pageNum && null != pageSize) { // 需要分页
            params.setPageNum(pageNum).setPageSize(pageSize);
            Page<ReliefNurseStandard> page = this.service.selectReliefNurseStandardPage(params);
            datas = page.getRecords();
        } else {
            datas = this.service.selectReliefNurseStandardList(params);
        }

        String fileName = "";
        byte[] data = null;
        try {
            ExcelMate excelMate = excelService.export(datas);
            fileName = excelMate.getFullName();
            data = excelMate.getData();
            excelMate = null;
        } catch (Exception e) {
            log.warn("数据导出失败", e);
        }

        try {
            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));
            OutputStream output = response.getOutputStream();
            output.write(data);
            output.flush();
        } catch (IOException e) {
            log.warn("文件下载失败", e);
        }

    }

}