package net.jqsoft.zhmz.cloudplatform.msgs.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jqsoft.apis.controller.SuperController;
import net.jqsoft.apis.util.QueryParams;
import net.jqsoft.common.domain.DataRespond;
import net.jqsoft.common.domain.ListRespond;
import net.jqsoft.common.domain.MsgRespond;
import net.jqsoft.common.lang.Converter;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.ModuleType;
import net.jqsoft.zhmz.cloudplatform.msgs.commons.enums.OPerateType;
import net.jqsoft.zhmz.cloudplatform.msgs.config.RocketMQProperties;
import net.jqsoft.zhmz.cloudplatform.msgs.manager.AsyncManager;
import net.jqsoft.zhmz.cloudplatform.msgs.manager.factory.AsyncFactory;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsLedgerTotal;
import net.jqsoft.zhmz.cloudplatform.msgs.rocketmq.model.LedgerMessageDto;
import net.jqsoft.zhmz.cloudplatform.msgs.rocketmq.producer.LedgerProducer;
import net.jqsoft.zhmz.cloudplatform.msgs.service.IMsgsLedgerTotalService;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.CommonUtil;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.ResponseUtil;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.StringUtils;
import net.jqsoft.zhmz.common.service.remote.RemoteAreaService;
import net.jqsoft.zhmz.common.vo.AreaVO;
import net.jqsoft.zhmz.common.vo.UserVO;
import org.jsets.fastboot.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023-12-12 11:32:04
 */
@Slf4j
@RestController
@RequestMapping("/msgs_ledger_total")
public class MsgsLedgerTotalController extends SuperController<String, MsgsLedgerTotal, IMsgsLedgerTotalService> {

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private RemoteAreaService remoteAreaService;

    @Autowired
    private LedgerProducer ledgerProducer;

    @Autowired
    private RocketMQProperties rocketMQProperties;

    /**
     * 增加
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public MsgRespond insert(@RequestBody @Validated MsgsLedgerTotal entity) {
        this.service.save(entity);
        return this.successRespond(MSG_INSERT_SUCCEED);
    }

    /**
     * 删除
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public MsgRespond delete(@PathVariable("id") String id) {
        long startTime = System.currentTimeMillis();
        this.service.deleteDataById(id);
        //记录操作日志
        AsyncManager.me().execute(AsyncFactory.recordOper(ModuleType.RELIEF_LEDGER, OPerateType.DELETE, id, true, "删除成功", startTime, CommonUtil.getIpAddress(httpServletRequest), CommonUtil.getBrower(httpServletRequest)));
        return this.successRespond(MSG_DELETE_SUCCEED);
    }

    /**
     * 批量删除
     */
    @DeleteMapping(value = "/delete_by_ids/{ids}", produces = MediaType.APPLICATION_JSON_VALUE)
    public MsgRespond deleteByIds(@PathVariable("ids") String[] ids) {
        this.service.deleteDataByIds(ids);
        return this.successRespond(MSG_DELETE_SUCCEED);
    }

    /**
     * 修改
     */
    @PutMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public MsgRespond update(@RequestBody @Validated MsgsLedgerTotal entity) {
        MsgsLedgerTotal updateEntity = this.service.selectOne(entity.getId());
        if (updateEntity.getStatus() != entity.getStatus()) {
            this.service.updateData(entity);
        }

        return this.successRespond(MSG_UPDATE_SUCCEED);
    }

    /**
     * 根据ID获取
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public DataRespond<MsgsLedgerTotal> get(@PathVariable("id") String id) {
        return this.dataRespond(this.service.selectOne(id));
    }

    /**
     * 根据条件获取列表
     * 如果page_num和page_size都不为空进行分页查询否则进行列表查询
     */
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ListRespond<MsgsLedgerTotal> list(@RequestParam(name = "page_num", required = false) Integer pageNum, @RequestParam(name = "page_size", required = false) Integer pageSize, @RequestParam(name = "areaCode", required = false) String areaCode, @RequestParam(name = "yearMonth", required = false) String yearMonth, @RequestParam(name = "status", required = false) String status, @RequestParam(name = "familyCategoryVal", required = false) String familyCategoryVal) {

        QueryParams params = QueryParams.build();
        if (StringUtils.isEmpty(areaCode)) {
            UserVO userVO = SecurityUtils.getAccount();
            areaCode = userVO.getAreaId();
        }

        AreaVO areaVO = remoteAreaService.getById(areaCode).getData();
        params.add("areaCode", areaCode);
        params.add("areaLevel", areaVO.getLevel());

        if (StringUtils.isNotEmpty(yearMonth)) {
            params.add("year", yearMonth.split("-")[0]);
            params.add("month", yearMonth.split("-")[1]);
        }
        params.add("status", status);
        params.add("familyCategoryVal", familyCategoryVal);

        if (null != pageNum && null != pageSize) { // 需要分页
            params.setPageNum(pageNum).setPageSize(pageSize);
            return this.pageRespond(this.service.selectMsgsLedgerTotalPage(params));
        }
        // 渲染列表数据的结果
        return this.listRespond(this.service.selectMsgsLedgerTotalList(params));
    }

    /**
     * 导出
     */
    @ApiOperation("导出（过滤条件同列表接口）")
    @GetMapping("/export")
    public void excelExportResult(HttpServletResponse response, @RequestParam Map<String, Object> queryParams) {
        QueryParams params = new QueryParams();
        params.putAll(queryParams);
        String areaCode = Converter.toString(params.get("areaCode"));
        if (StringUtils.isEmpty(areaCode)) {
            UserVO userVO = SecurityUtils.getAccount();
            areaCode = userVO.getAreaId();
        }

        AreaVO areaVO = remoteAreaService.getById(areaCode).getData();
        params.add("areaCode", areaCode);
        params.add("areaLevel", areaVO.getLevel());

        String yearMonth = Converter.toString(params.get("yearMonth"));
        if (StringUtils.isNotEmpty(yearMonth)) {
            params.add("year", yearMonth.split("-")[0]);
            params.add("month", yearMonth.split("-")[1]);
        }
        ResponseUtil.writeExcelResponse(response, this.service.selectMsgsLedgerTotalList(params), "救助金发放台帐");
    }

    /**
     * 生成台账
     */
    @PostMapping(value = "/generate_ledger", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public MsgRespond generateCareLedger(@RequestBody @Validated LedgerMessageDto params) {
        UserVO userVO = SecurityUtils.getAccount();
        params.setUserInfo(userVO);
        params.setIpAddress(CommonUtil.getIpAddress(httpServletRequest));
        params.setBrowser(CommonUtil.getBrower(httpServletRequest));
        RocketMQProperties.Producer ledgerProducerInfo = rocketMQProperties.getProducers().get("ledgerProducer");
        if (ledgerProducerInfo != null && ledgerProducerInfo.getEnable()) {
            ledgerProducer.sendGenerateMessage(params);
            return this.successRespond("您的台账生成任务已提交，系统正在生成中，请稍候完成");
        } else {
            this.service.generateLedger(params);
            return this.successRespond("台账已生成成功");
        }
    }
}