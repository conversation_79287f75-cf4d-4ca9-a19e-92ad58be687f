#----------------------     默认环境     -----------------------
server:
  port: 8066 #端口
  #servlet:
  #  context-path: /  #路径上下文
cxf:
  path: /webService
#----------------------     系统配置        -----------------------
net:
  jqsoft:
    constants: #系统常量配置
      respond-decrypt-private-key: e2691fc28eda0e4fb5ba0092a72dbfbde9d517ff21680b44212e43c3aba5709 #SM2私钥 解密用
      respond-encrypt-key: 0495f30a0752abe637302c243c91d854ae1c5acce7c03698024c60288c3b4a071cd32f67386c948c671f399c7db0c6875c16b603ed24df2ce08ea5ad50a3246e46 #SM2公钥 #响应加密KEY
      respond-encrypt-algorithm: sm2 #响应加密算法支持des aes sm4(国密)
    internal:
      cors-enabled: true #是否允许跨域访问
      remote-auth-enabled: true #启用远程鉴权
      annotation-enabled: true #启用注解鉴权
      token-sign-key: abcdee78985eve*fdafec #令牌签名密钥
      hmac-sign-key: abcdee78985eve*fdafec #HMAC签名密钥
      auth-rules:
        - /ping/**-->anon
        - /oauth2/**-->anon
        - /open/public_familyinfo/**-->anon

#----------------------      SPRING       -----------------------
spring:
  profiles:
    active: dev  #dev-为开发环境、test-为测试环境、prod-为正式测试环境，上线时请切换,切记！！！
  devtools:
    restart:
      enabled: true #启用热启动
  application:
    name: zhmz-cloudplatform-msgs #民政云平台服务
  servlet:
    multipart: # 附件大小限制
      max-file-size: 10MB
      max-request-size: 10MB
#----------------------     第三方配置        -----------------------
third-party:
  sm4: # SM4国密加密配置
    default-key: xy8JhrE4PpaFWR9NiCq4WQ== # SM4默认加密密钥（Base64编码）
    algorithm: SM4 # 加密算法
    transformation: SM4/ECB/PKCS7Padding # 加密转换模式
    provider: BC # 加密提供者（BouncyCastle）
    key-size: 128 # 密钥长度（位）
    enabled: true # 是否启用SM4加密

  oauth2: # OAuth2客户端配置
    clients:
      - client-id: zhmz-msgs-client # 客户端ID
        client-name: 民政云平台消息服务客户端 # 客户端名称
        client-secret: zhmz-msgs-secret-2025 # 客户端密钥
        scopes: read,write,admin # 权限范围
        grant-types: client_credentials # 授权类型
        access-token-validity: 7200 # 访问令牌有效期（秒，2小时）
        enabled: true # 是否启用
        remark: 民政云平台消息服务默认客户端 # 备注

#----------------------      其他        -----------------------
ribbon:
  ConnectTimeout: 6000
  ReadTimeout: 6000
  OkToRetryOnAllOperations: false
  MaxAutoRetriesNextServer: 2
  MaxAutoRetries: 1
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 12000
feign:
  hystrix:
    enabled: true