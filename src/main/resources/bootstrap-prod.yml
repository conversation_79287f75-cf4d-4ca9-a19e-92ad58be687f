#----------------------     开发环境     -----------------------
#----------------------     日志配置        -----------------------
logging:
  file:
    path: /opt/app/zhmz-cloudplatform-msgs/logs #日志文件目录
#----------------------     系统配置        -----------------------
net:
  jqsoft:
    datasource: #数据源配置（这里是开发库的地址）
      multi:
        - primary: true #主数据源
          ds-name: INTEGRATERELIEF
          driver-class-name: com.zdxlz.dplugin.core.DPluginDriver
          url: jdbc:dm:zdxlz://172.18.5.157:5236
          username: <PERSON><PERSON>(QLA50uEioYVwT13Uvj9wOw==)
          password: <PERSON><PERSON>(xMZ5K+/qsQOWtb8qwC9rH7qOCRPaU+lH)
          validationQuery: SELECT 1
        - ds-name: CLOUDPLATFORM_WARN
          driver-class-name: com.zdxlz.dplugin.core.DPluginDriver
          url: jdbc:dm:zdxlz://172.18.5.157:5236
          username: ENC(QLA50uEioYVwT13Uvj9wOw==)
          password: ENC(xMZ5K+/qsQOWtb8qwC9rH7qOCRPaU+lH)
          validationQuery: SELECT 1 FROM DUAL
    mybatis: #mybatis配置
      mapper-locations: classpath:mapper/*.xml,classpath:mapper/*/*.xml #映射地址
      type-aliases-package: net.jqsoft.zhmz.cloudplatform.msgs.model.entity #实体包
      configuration:
        #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #控制台打印SQL,上线请注释掉
        variables:
          databaseName: ZHMZ_INTEGRATERELIEF
      global-config:
        banner: false
        db-config:
          id-type: ASSIGN_ID #主键类型  AUTO:"数据库ID自增"、INPUT:"用户输入ID",、ASSIGN_UUID:"UUID"、ASSIGN_ID:"系统内置的全局ID"
          schema: ZHMZ_INTEGRATERELIEF #schema名称
    swagger:
      enable: true
      application-name: ${spring.application.name}
      application-version: 1.0
      application-description: ${spring.application.name} 接口说明
      try-host: http://localhost:${server.port}
    scheduled: #定时任务配置
      enable: true #是否启用定时任务
      updatereliefamount: #批量更新金额（val:3）
        enable: false       # 是否启用
        executeNum: 1 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 1000 # 时间间隔（毫秒）
      batchaudit: #批量审核审批（val:0,1）
        enable: true       # 是否启用
        executeNum: 1 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 120000 # 时间间隔（毫秒）
      batchimport: #批量导入（val:6）
        enable: true       # 是否启用
        executeNum: 1 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 120000 # 时间间隔（毫秒）
      generateledger: #批量生成台帐（val:4）
        enable: true       # 是否启用
        executeNum: 1 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 120000 # 时间间隔（毫秒）
      generatereport: #批量生成报表（val:2）
        enable: true       # 是否启用
        executeNum: 1 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 120000 # 时间间隔（毫秒）
      batchexport: #批量导出
        enable: true       # 是否启用
        executeNum: 1 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 120000 # 时间间隔（毫秒）
      planEffective: #定时生效
        enable: false       # 是否启用
        executeNum: 2 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 30000 # 时间间隔（毫秒）
      dynamicreview: #
        enable: true       # 是否启用
        executeNum: 2 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 30000 # 时间间隔（毫秒）
      dynamicexpiration: #
        enable: true       # 是否启用
      cardgenerate: #
        enable: true       # 是否启用
        executeNum: 2 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 30000 # 时间间隔（毫秒）
      productDifficulty: #定时归集其他困难人员
        enable: false       # 是否启用
        executeNum: 20 # 每次执行任务数
        threadNum: 2 # 执行线程数
        fixedRate: 300000 # 时间间隔（毫秒）
      dynamicsign: #动态生存认证打卡生成 定时生效
        enable: false       # 是否启用
      checkfamily: #核对数据同步
        enable: false
      checkstart: #发起核对
        enable: false
#----------------------      SPRING        -----------------------
spring:
  redis:
    timeout: 6000ms
    host: ************
    port: 6379
    password: ENC(+Nqj4a4FN943O5h73GR94YGZr1ealN/r)
    database: 2
    lettuce:
      pool:
        max-active: 1000   # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10       # 连接池中的最大空闲连接
        max-wait: -1       # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 5       # 连接池中的最小空闲连接
  cloud:
    #    inetutils:
    #      preferred-networks: 10.1.71
    nacos:
      config:
        server-addr: ************:8848 #配置中心地址
        prefix: ${spring.application.name}
        file-extension: yaml
        ext-config:
          - data-id: zhmz-common.properties
            refresh: true
      discovery:
        server-addr: ************:8848 #注册中心地址

#----------------------      全局ID        -----------------------
uidgenerator:
  driverClassName: com.zdxlz.dplugin.core.DPluginDriver
  url: jdbc:dm:zdxlz://172.18.5.157:5236/ZHMZ_UID?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
  username: ENC(QLA50uEioYVwT13Uvj9wOw==)
  password: ENC(xMZ5K+/qsQOWtb8qwC9rH7qOCRPaU+lH)
  schema: ZHMZ_UID #schema名称
word:
  templatePath:
    dbNotice: template/electronicRecords/最低生活保障确认通知书.docx
    tbNotice: template/electronicRecords/青海省特困人员供养金停发告知书.docx
# PDF签章服务配置
pdf:
  signature:
    # 签章服务器配置
    server:
      # 签章服务器地址
      url: http://59.220.179.223:81/anysign-server-sign/seal/v1/signature
      # 连接超时时间（毫秒）
      connect-timeout: 30000
      # 读取超时时间（毫秒）
      read-timeout: 60000
      # 重试次数
      retry-count: 3

    # 默认签章配置
    default:
      # 默认X坐标
      x: 450.0
      # 默认Y坐标
      y: 350.0
      # 默认页号（从0开始）
      page: 0
      # 默认签章ID（实际使用时需要配置真实的签章ID）
      sealId: 63010400002793
      keyword: 盖章
#----------------------     核对配置        -----------------------
verify:
  webservices:
    wonders: # 核对信息系统（万达信息股份有限公司）
      wsdl-url: http://************:9204/webService/VerifyProcessService?wsdl
      endpoint-address: http://************:9204/webService/VerifyProcessService
      secret-key: db1234
      system-code: 1004
      ws-secret-key: dfgsdgkklyuydtr4546uga5454s
#----------------------  消息队列-----------------
rocketmq:
  nameServerAddr: ************:9876
  producers:
    ledgerProducer:
      enable: true
      topic: ledger_topic
      group: ledger_producer_group

  consumers:
    ledgerGenerateConsumer:
      enable: true
      topic: ledger_topic #逻辑消息通道，最大范围（一级分类）
      group: ledger_generate_consumer_group #消费者分组，实现负载均衡，中间范围
      tag: generate #细粒度消息过滤，最小范围（二级过滤）
