<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20170720" releaseVersion="10.0.0">
<TableDataMap>
<TableData name="baseinfo" class="com.fr.data.impl.DBTableData">
<Parameters>
<Parameter>
<Attributes name="id"/>
<O>
<![CDATA[1920677758854361089]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[qinghai]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.batch_no,a.HOUSEHOLDER as name,a.HOUSEHOLDER_ID_CARD as cardNo,a.FAMILY_ADDRESS as familyAddress,a.TEL as phone,a.MODIFY_DATE AS editDate 
,a.PERSON_COUNT AS familyNumber,CASE WHEN SUBSTRING(a.HOUSEHOLDER_ID_CARD, 17, 1)%2==0 THEN '女' ELSE '男' END AS sex
,(select WM_CONCAT(FAMILY_CATEGORY_KEY) FROM PUBLIC_FAMILYDISTRIBUTION WHERE FAMILY_ID=a.id and DELETE_MARK =0) AS familyCategory,a.id
from PUBLIC_FAMILYINFO a where id = '${id}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="areaName" class="com.fr.data.impl.DBTableData">
<Parameters>
<Parameter>
<Attributes name="id"/>
<O>
<![CDATA[1920677758854361089]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[qinghai]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select sa.name from ZHMZ_BASIC.t_area sa left join ZHMZ_BASIC.t_USER su on su.AREA_ID = sa.ID 
left join PUBLIC_FAMILYINFO a on a.MODIFY_USER_ID = su.id where a.id = '${id}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebPageContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.page.First">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-First')}]]></Text>
<IconName>
<![CDATA[first]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Previous">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Previous')}]]></Text>
<IconName>
<![CDATA[previous]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.PageNavi">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
</Widget>
<Widget class="com.fr.report.web.button.page.Next">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Next')}]]></Text>
<IconName>
<![CDATA[next]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Last">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Last')}]]></Text>
<IconName>
<![CDATA[last]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.FlashPrint">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Utils-Print[Client]A')}]]></Text>
<IconName>
<![CDATA[flashprint]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.PDFPrint">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Utils-Print[Client]A')}]]></Text>
<IconName>
<![CDATA[pdfprint]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<IconName>
<![CDATA[export]]></IconName>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<WebPage isPage="false" showAsImage="false" autoScale="false" tdHeavy="false"/>
</WebPageContent>
</ReportWebAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1143000,304800,2286000,1180800,1180800,1180800,1180800,1180800,1180800,1371600,1180800,1714500,723900,723900,723900,342900,2209800,1180800,1180800,1180800,1180800,1180800,1180800,1371600,1180800,1713600,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3543300,1728000,876300,1728000,723900,1728000,1371600,1676400,3543300,1728000,838200,1728000,800100,1371600,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="2">
<O>
<![CDATA[第一联办理人存留]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<Present class="com.fr.report.cell.cellattr.BarcodePresent">
<BarcodeAttr height="33" draw="false" RCodeVersion="7" RCodeErrorCorrect="1" RcodeDrawPix="1"/>
</Present>
<Expand dir="0"/>
</C>
<C c="1" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="1" cs="3" rs="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE($qrcodePath, "&id=", $id)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.report.cell.cellattr.BarcodePresent">
<BarcodeAttr type="16" height="33" draw="false" RCodeVersion="0" RCodeErrorCorrect="1" RcodeDrawPix="2"/>
</Present>
<Expand/>
</C>
<C c="0" r="2">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="2" cs="2" s="1">
<O>
<![CDATA[编号：]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="2" cs="6" s="2">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="BATCH_NO"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="3" cs="11" s="3">
<O>
<![CDATA[           社会救助受理回执单]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="4" s="4">
<O>
<![CDATA[申请人姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="4" cs="6" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="4" s="4">
<O>
<![CDATA[性别]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="SEX"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="4" cs="2" s="5">
<O>
<![CDATA[家庭人口]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="4" cs="3" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="FAMILYNUMBER"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="5" s="4">
<O>
<![CDATA[身份证号码]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="5" cs="7" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="CARDNO"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="5" s="4">
<O>
<![CDATA[联系电话]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="5" cs="5" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="PHONE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="6" s="4">
<O>
<![CDATA[家庭住址]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="6" cs="13" s="5">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="FAMILYADDRESS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="7" s="4">
<O>
<![CDATA[申请救助项]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="7" cs="13" s="5">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="FAMILYCATEGORY"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="8" s="4">
<O>
<![CDATA[受理机构]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="8" cs="7" s="4">
<O t="DSColumn">
<Attributes dsName="areaName" columnName="NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="8" s="4">
<O>
<![CDATA[受理日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="8" cs="5" s="6">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="EDITDATE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="9" s="4">
<O>
<![CDATA[申请人签字]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="9" cs="7" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="9" s="4">
<O>
<![CDATA[办理人签字]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="9" cs="5" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="10" s="4">
<O>
<![CDATA[备注]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="10" cs="13" s="7">
<O>
<![CDATA[此回执单为你本次申请的受理凭证，不作为最终审批的依据]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="11" cs="14">
<O>
<![CDATA[此回执单为你本次申请的受理凭证，不作为最终审批的依据]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="12" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="13" cs="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="14" cs="2">
<O>
<![CDATA[第二联申请人存留]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="15" s="0">
<PrivilegeControl/>
<Present class="com.fr.report.cell.cellattr.BarcodePresent">
<BarcodeAttr height="33" draw="false" RCodeVersion="7" RCodeErrorCorrect="1" RcodeDrawPix="1"/>
</Present>
<Expand dir="0"/>
</C>
<C c="1" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="15">
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="15" cs="3" rs="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE($qrcodePath, "&id=", $id)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.report.cell.cellattr.BarcodePresent">
<BarcodeAttr type="16" height="33" draw="false" RCodeVersion="0" RCodeErrorCorrect="1" RcodeDrawPix="2"/>
</Present>
<Expand/>
</C>
<C c="0" r="16">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="16">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="16">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="16" cs="2" s="1">
<O>
<![CDATA[编号：]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="16" cs="6" s="2">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="BATCH_NO"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="17" cs="11" s="3">
<O>
<![CDATA[           社会救助受理回执单]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="18" s="4">
<O>
<![CDATA[申请人姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="18" cs="6" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="18" s="4">
<O>
<![CDATA[性别]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="18" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="SEX"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="18" cs="2" s="4">
<O>
<![CDATA[家庭人口]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="18" cs="3" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="FAMILYNUMBER"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="19" s="4">
<O>
<![CDATA[身份证号码]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="19" cs="7" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="CARDNO"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="19" s="4">
<O>
<![CDATA[联系电话]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="19" cs="5" s="4">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="PHONE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="20" s="4">
<O>
<![CDATA[家庭住址]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="20" cs="13" s="5">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="FAMILYADDRESS"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="21" s="4">
<O>
<![CDATA[申请救助项]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="21" cs="13" s="9">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="FAMILYCATEGORY"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="22" s="4">
<O>
<![CDATA[受理机构]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="22" cs="7" s="4">
<O t="DSColumn">
<Attributes dsName="areaName" columnName="NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="22" s="4">
<O>
<![CDATA[受理日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="22" cs="5" s="6">
<O t="DSColumn">
<Attributes dsName="baseinfo" columnName="EDITDATE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="23" s="4">
<O>
<![CDATA[申请人签字]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="23" cs="7" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="23" s="4">
<O>
<![CDATA[办理人签字]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="23" cs="5" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="24" s="4">
<O>
<![CDATA[备注]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="24" cs="13" s="7">
<O>
<![CDATA[此回执单为你本次申请的受理凭证，不作为最终审批的依据]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="25" cs="14">
<O>
<![CDATA[此回执单为你本次申请的受理凭证，不作为最终审批的依据]]></O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<Background name="ColorBackground" color="-1"/>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="false"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
</ReportParameterAttr>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="楷体" style="1" size="128"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="楷体" style="1" size="144"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.SimpleDateFormatThreadSafe">
<![CDATA[yyyy年MM月dd日]]></Format>
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="华文隶书" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="14"/>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesignerVersion DesignerVersion="KAA"/>
<PreviewType PreviewType="0"/>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="a03b14f5-5334-41d9-b7ee-0407bde0ea1a"/>
</TemplateIdAttMark>
</WorkBook>
