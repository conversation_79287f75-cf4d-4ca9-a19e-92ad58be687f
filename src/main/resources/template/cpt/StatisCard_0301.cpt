<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20170720" releaseVersion="10.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Parameters>
<Parameter>
<Attributes name="areaCode"/>
<O>
<![CDATA[341501001]]></O>
</Parameter>
<Parameter>
<Attributes name="month"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="year"/>
<O>
<![CDATA[2024]]></O>
</Parameter>
<Parameter>
<Attributes name="familyCategoryVal"/>
<O>
<![CDATA[01]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[FRDemo]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT FAMILY_ID,"YEA<PERSON>", "MON<PERSON>", COUNTY_CODE, TOWN_CODE, VILLAGE_CODE, VILLAGE_NAME,
                (SELECT id FROM ZHMZ_BASIC.T_COMPARE_AREA WHERE compare_sys='2' AND DELETE_FLAG =0 AND AREA_ID=VILLAGE_CODE) || SUBSTR(NUMBER_CODE,13)  AS NUMBER_CODE, CATEGORY_CODE, FAMILY_CATEGORY_VAL,
                FAMILY_CATEGORY_KEY, FAMILY_SUBCATEGORY_KEY, FAMILY_SUBCATEGORY_VAL, APPLY_DATE, CURRENT_START_DATE, I_D_NUMBER, HOUSEHOLDER_ID_CARD,
                HOUSEHOLDER, CASE WHEN SEX_CODE=0 THEN 1 ELSE 2 END AS SEX_CODE, SEX_KEY, NATIONALITY_KEY, NATIONALITY_VAL, FAMILY_ADDRESS, PERSON_COUNT_ON_FLOW, TEL, IS_THREE_NO_PERSON,
                OLD_PEOPLE, ADULT_PEOPLE, ADULT_WORKING, ADULTSFLEXIBLE_EMPLOYMENT, LOSE_JOB_ADULT, NOT_REGISTER_LOSE_JOB, HAVE_LABOUR_CAPACITY,
                NO_LABOUR_CAPACITY, JUVENILES, STUDY_JUVENILES, WOMAN, JUVENILES_OTHER, DISABLED, SEVERELY_DISABLED, SUBSIDY_STANDARD, FINAL_MONEY,
                BANK_VAL, BANK_KEY, BANK_ACCOUNT, HOUSE_HOLDERISFLOW, CLASSIFY_FUND, RELIEF_FUND, RAISE_SUBSIDY, MONTH_INCOME, PERSON_AVG_INCOME,
                ALL_CARE, PART_PROVIDE, ALL_PROVIDE, POOR_PERSON, MEMBER_NAME1, MEMBER_NAME2, MEMBER_NAME3, MEMBER_NAME4, MEMBER_NAME5, MEMBER_NAME6,
                MEMBER_NAME7, MEMBER_NAME8, MEMBER_NAME9, MEMBER_CARD1, MEMBER_CARD2, MEMBER_CARD3, MEMBER_CARD4, MEMBER_CARD5,
                MEMBER_CARD6, MEMBER_CARD7, MEMBER_CARD8, MEMBER_CARD9
                FROM ZHMZ_INTEGRATERELIEF.MSGS_DECCA_DETAIL
                WHERE DELETE_MARK=0 and FAMILY_CATEGORY_VAL ='${familyCategoryVal}' and TOWN_CODE = '${areaCode}' and YEAR = '${year}' and MONTH ='${month}'
              ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebPageContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.page.First">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-First')}]]></Text>
<IconName>
<![CDATA[first]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Previous">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Previous')}]]></Text>
<IconName>
<![CDATA[previous]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.PageNavi">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
</Widget>
<Widget class="com.fr.report.web.button.page.Next">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Next')}]]></Text>
<IconName>
<![CDATA[next]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Last">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Last')}]]></Text>
<IconName>
<![CDATA[last]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.FlashPrint">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('FR-Engine_Utils_Flash_Print[Client]A')}]]></Text>
<IconName>
<![CDATA[flashprint]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Print">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Print')}]]></Text>
<IconName>
<![CDATA[print]]></IconName>
<PrintButtons/>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<IconName>
<![CDATA[export]]></IconName>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
<Widget class="com.fr.report.web.button.Email">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Email')}]]></Text>
<IconName>
<![CDATA[email]]></IconName>
<EmailButton customConsignee="true" consigneeByDepartment="false" consigneeByRole="false"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<WebPage isPage="true" showAsImage="false" autoScale="false" tdHeavy="false"/>
</WebPageContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1638300,1257300,1181100,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,0,6048000,6048000,6048000,6048000,6400800,4343400,5448300,6705600,3581400,6705600,3771900,3771900,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,4762500,5372100,3695700,5143500,4343400,4876800,5029200,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,6705600,5372100,3695700,4419600,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" cs="50" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$year + "年" + $month + "月" + "农村特困台卡信息表"]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="3">
<O>
<![CDATA[Layer]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[AreaCode]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="4">
<O>
<![CDATA[_QYDM]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="4">
<O>
<![CDATA[F1_1]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="4">
<O>
<![CDATA[F1_2]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="4">
<O>
<![CDATA[F1_3]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="4">
<O>
<![CDATA[F1_4]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="4">
<O>
<![CDATA[F1_5]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="4">
<O>
<![CDATA[F1_6]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="1" s="4">
<O>
<![CDATA[F1_7]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="1" s="4">
<O>
<![CDATA[F1_8]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="1" s="4">
<O>
<![CDATA[F1_9]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="1" s="4">
<O>
<![CDATA[F1_10]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="1" s="4">
<O>
<![CDATA[F1_11]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="14" r="1" s="4">
<O>
<![CDATA[F1_13]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="15" r="1" s="4">
<O>
<![CDATA[F1_28]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="16" r="1" s="4">
<O>
<![CDATA[F1_14]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="17" r="1" s="4">
<O>
<![CDATA[F1_12]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="18" r="1" s="4">
<O>
<![CDATA[F1_15]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="19" r="1" s="4">
<O>
<![CDATA[F1_70]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="20" r="1" s="4">
<O>
<![CDATA[F1_71]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="21" r="1" s="4">
<O>
<![CDATA[F1_72]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="22" r="1" s="4">
<O>
<![CDATA[F1_16]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="23" r="1" s="4">
<O>
<![CDATA[F1_17]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="24" r="1" s="4">
<O>
<![CDATA[F1_18]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="25" r="1" s="4">
<O>
<![CDATA[F1_19]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="26" r="1" s="4">
<O>
<![CDATA[F1_20]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="27" r="1" s="4">
<O>
<![CDATA[F1_21]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="28" r="1" s="4">
<O>
<![CDATA[F1_23]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="29" r="1" s="4">
<O>
<![CDATA[F1_24]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="30" r="1" s="4">
<O>
<![CDATA[F1_25]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="31" r="1" s="4">
<O>
<![CDATA[F1_26]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="32" r="1" s="4">
<O>
<![CDATA[F1_60]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="33" r="1" s="4">
<O>
<![CDATA[F1_40]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="34" r="1" s="4">
<O>
<![CDATA[F1_41]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="35" r="1" s="4">
<O>
<![CDATA[F1_42]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="36" r="1" s="4">
<O>
<![CDATA[F1_43]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="37" r="1" s="4">
<O>
<![CDATA[F1_44]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="38" r="1" s="4">
<O>
<![CDATA[F1_45]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="39" r="1" s="4">
<O>
<![CDATA[F1_46]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="40" r="1" s="4">
<O>
<![CDATA[F1_47]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="41" r="1" s="4">
<O>
<![CDATA[F1_48]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="42" r="1" s="4">
<O>
<![CDATA[F1_49]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="43" r="1" s="4">
<O>
<![CDATA[F1_50]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="44" r="1" s="4">
<O>
<![CDATA[F1_51]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="45" r="1" s="4">
<O>
<![CDATA[F1_52]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="46" r="1" s="4">
<O>
<![CDATA[F1_53]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="47" r="1" s="4">
<O>
<![CDATA[F1_54]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="48" r="1" s="4">
<O>
<![CDATA[F1_55]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="49" r="1" s="4">
<O>
<![CDATA[F1_56]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="50" r="1" s="4">
<O>
<![CDATA[F1_57]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="51" r="1" s="4">
<O>
<![CDATA[F1_27]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="AREA_LEVEL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="AREA_CODE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="NUMBER_CODE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="AreaCode"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B3]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="YearMonth"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$YearMonth]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="FamilyCategoryVal"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$FamilyCategoryVal]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_self]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true">
<![CDATA[/StatisGrantFund.cpt]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="APPLY_DATE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="FAMILY_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="HOUSEHOLDER_ID_CARD"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="HOUSEHOLDER"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="SEX_CODE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="NATIONALITY_VAL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="FAMILY_ADDRESS"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="2" s="6">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="TEL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="2" s="6">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="13" r="2" s="6">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="14" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="OLD_PEOPLE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="15" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="ADULT_PEOPLE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="16" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="JUVENILES"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="17" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="WOMAN"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="18" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="DISABLED"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="19" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="ALL_PROVIDE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="20" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="PART_PROVIDE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="21" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="ALL_CARE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="22" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="FINAL_MONEY"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="23" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="FINAL_MONEY"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="24" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="CLASSIFY_FUND"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="25" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="RELIEF_FUND"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="26" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MONTH_INCOME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="27" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="SUBSIDY_STANDARD"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="28" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName=" BANK_KEY"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="29" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="BANK_ACCOUNT"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="30" r="2" s="6">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="31" r="2" s="6">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="32" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="HOUSE_HOLDERISFLOW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="33" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME1"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="34" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD1"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="35" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME2"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="36" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD2"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="37" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME3"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="38" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD3"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="39" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME4"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="40" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD4"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="41" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME5"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="42" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD5"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="43" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME6"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="44" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD6"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="45" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME7"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="46" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD7"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="47" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME8"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="48" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD8"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="49" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_NAME9"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="50" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="MEMBER_CARD9"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="51" r="2" s="6">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting orientation="1">
<PaperSize width="33120000" height="50400000"/>
<Margin top="864000" left="1728000" bottom="864000" right="1728000"/>
</PaperSetting>
<Background name="ColorBackground" color="-1"/>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="false"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="120"/>
<Background name="ColorBackground" color="-2171170"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="ColorBackground" color="-2171170"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="ColorBackground" color="-2171170"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72" foreground="-16776961" underline="1"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
</StyleList>
<DesignerVersion DesignerVersion="KAA"/>
<PreviewType PreviewType="0"/>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="f02731b4-05df-4fe9-8edf-5306bc410b60"/>
</TemplateIdAttMark>
</WorkBook>
