<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20170720" releaseVersion="10.0.0">
<TableDataMap>
<TableData name="baseInfo" class="com.fr.data.impl.DBTableData">
<Parameters>
<Parameter>
<Attributes name="id"/>
<O>
<![CDATA[1920677758854361089]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[qinghai]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.batch_no,a.HOUSEHOLDER as name,a.HOUSEHOLDER_ID_CARD as cardNo,a.FAMILY_ADDRESS as familyAddress,a.TEL as phone,a.MODIFY_DATE AS editDate 
,a.PERSON_COUNT AS familyNumber,CASE WHEN SUBSTRING(a.HOUSEHOLDER_ID_CARD, 17, 1)%2==0 THEN '女' ELSE '男' END AS sex
,(select WM_CONCAT(FAMILY_CATEGORY_KEY) FROM PUBLIC_FAMILYDISTRIBUTION WHERE FAMILY_ID=a.id and DELETE_MARK =0) AS familyCategory,a.id,a.TOWN_NAME AS townName,a.VILLAGE_NAME AS villageName,a.NATIONALITY_KEY as nation
from PUBLIC_FAMILYINFO a where id = '${id}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="date" class="com.fr.data.impl.DBTableData">
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[qinghai]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select to_char(sysdate,'yyyy') as year, to_char(sysdate,'MM') as month,to_char(sysdate,'dd') as day  from dual]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="processResult" class="com.fr.data.impl.DBTableData">
<Parameters>
<Parameter>
<Attributes name="id"/>
<O>
<![CDATA[1920677758854361089]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[qinghai]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT a.FAMILY_CATEGORY_KEY AS name,'办理单位  ' ||  (SELECT d.NAME FROM ZHMZ_BASIC.T_DEPARTMENT d inner join ZHMZ_BASIC.t_USER su on su.DEPARTMENT_ID = d.ID AND su.id=a.MODIFY_USER_ID  ) || CHR(10) ||
        '办理时间  ' ||to_char(a.MODIFY_DATE ,'yyyy-MM-dd')||  CHR(10) || 
        '办理结果  ' ||(case when a.STATUS ='10' THEN '审批通过' 
        when a.STATUS='20' THEN '审批未通过' end) || CHR(10) || 
        '结果说明  '|| '实发金额'|| '(' || a.RELIEF_FUND || '元)' || 
        a.CHECK_OPINION as content
        FROM PUBLIC_FAMILYDISTRIBUTION a INNER JOIN PUBLIC_FAMILYINFO b ON b.id=a.FAMILY_ID WHERE family_id='${id}' AND a.DELETE_MARK =0]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebPageContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.page.First">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-First')}]]></Text>
<IconName>
<![CDATA[first]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Previous">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Previous')}]]></Text>
<IconName>
<![CDATA[previous]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.PageNavi">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
</Widget>
<Widget class="com.fr.report.web.button.page.Next">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Next')}]]></Text>
<IconName>
<![CDATA[next]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Last">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('ReportServerP-Last')}]]></Text>
<IconName>
<![CDATA[last]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.FlashPrint">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Utils-Print[Client]A')}]]></Text>
<IconName>
<![CDATA[flashprint]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.PDFPrint">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Utils-Print[Client]A')}]]></Text>
<IconName>
<![CDATA[pdfprint]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr description="">
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<IconName>
<![CDATA[export]]></IconName>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<WebPage isPage="false" showAsImage="false" autoScale="false" tdHeavy="false"/>
</WebPageContent>
</ReportWebAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2095500,1371600,152400,1562100,1295400,1371600,1409700,1866900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4876800,3848100,3505200,1981200,3695700,2171700,1066800,914400,876300,876300,647700,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="11" s="0">
<O>
<![CDATA[结果通知单]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="TOWNNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[乡镇(街道办) ]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" cs="2" s="1">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="VILLAGENAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[村居(委员会)]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="date" columnName="YEAR"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[年]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="date" columnName="MONTH"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="1" s="4">
<O>
<![CDATA[月]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="date" columnName="DAY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="1" s="4">
<O>
<![CDATA[日]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="3" s="5">
<O>
<![CDATA[申请人姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="3" s="5">
<O>
<![CDATA[性别]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="3" cs="2" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="SEX"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="3" s="5">
<O>
<![CDATA[家庭人口]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="3" cs="5" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="FAMILYNUMBER"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="4" s="5">
<O>
<![CDATA[身份证号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="4" cs="4" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="CARDNO"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="4" s="5">
<O>
<![CDATA[民族]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="4" cs="5" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="NATION"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="4">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="5" s="5">
<O>
<![CDATA[家庭地址]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="5" cs="10" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="FAMILYADDRESS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="6" s="5">
<O>
<![CDATA[申请救助项]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="6" cs="10" s="5">
<O t="DSColumn">
<Attributes dsName="baseInfo" columnName="FAMILYCATEGORY"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="7" s="5">
<O>
<![CDATA[办理结果]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="7" s="6">
<O t="DSColumn">
<Attributes dsName="processResult" columnName="NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="7" cs="9" s="7">
<O t="DSColumn">
<Attributes dsName="processResult" columnName="CONTENT"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="10">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="12">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<Background name="ColorBackground" color="-1"/>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="false"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
</ReportParameterAttr>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="楷体" style="1" size="128"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="ColorBackground" color="-1"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1" spacingBefore="7" lineSpacing="7">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesignerVersion DesignerVersion="KAA"/>
<PreviewType PreviewType="0"/>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="7dab4286-dc54-4920-bd01-a315d2b6dd3d"/>
</TemplateIdAttMark>
</WorkBook>
