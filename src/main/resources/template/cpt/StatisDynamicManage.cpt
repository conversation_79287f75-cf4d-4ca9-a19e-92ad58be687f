<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20170720" releaseVersion="10.0.0">
<TableDataMap>
<TableData name="DynamicManagement" class="com.fr.data.impl.DBTableData">
<Parameters>
<Parameter>
<Attributes name="YearMonth"/>
<O>
<![CDATA[2023-12]]></O>
</Parameter>
<Parameter>
<Attributes name="FamilyCategoryVal"/>
<O>
<![CDATA[01]]></O>
</Parameter>
<Parameter>
<Attributes name="AreaCode"/>
<O>
<![CDATA[340000]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[JA_newdibao]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select * from ZHMZ_INTEGRATERELIEF.MSGS_STAT_DYNAMICMANAGE where YEAR_MONTH='${YearMonth}' and FAMILY_CATEGORY_VAL='${FamilyCategoryVal}' and (AREA_CODE='${AreaCode}' or PARENT_ID='${AreaCode}') order by AREA_LEVEL,AREA_CODE]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="3"/>
<FR/>
<HC/>
<FC/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1371600,1295400,1371600,1371600,1143000,2247900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,0,2171700,5562600,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,3600000,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" cs="18" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=INDEXOFARRAY(SPLIT($YearMonth,"-"),1) + "年" + INDEXOFARRAY(SPLIT($YearMonth,"-"),2) + "月" + if($FamilyCategoryVal = "01", "农保", if($FamilyCategoryVal = "02", "城保", "特困人员")) + "动态管理统计表"]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" rs="3" s="3">
<O>
<![CDATA[Layer]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" rs="3" s="4">
<O>
<![CDATA[AreaCode]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" rs="3" s="5">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" rs="3" s="5">
<O>
<![CDATA[地区]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" cs="4" s="6">
<O>
<![CDATA[新增对象情况]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" cs="8" s="6">
<O>
<![CDATA[退出对象情况]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="16" r="1" cs="2" s="6">
<O>
<![CDATA[对象补差调增情况]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="18" r="1" cs="2" s="6">
<O>
<![CDATA[对象补差调减情况]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="2" rs="2" s="5">
<O>
<![CDATA[当月人次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="2" rs="2" s="5">
<O>
<![CDATA[当月金额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="2" rs="2" s="5">
<O>
<![CDATA[1-本月累计人次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="2" rs="2" s="5">
<O>
<![CDATA[1-本月累计金额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="2" rs="2" s="5">
<O>
<![CDATA[当月人次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="2" rs="2" s="5">
<O>
<![CDATA[当月金额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="2" cs="4" s="5">
<O>
<![CDATA[退出原因]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="14" r="2" rs="2" s="5">
<O>
<![CDATA[1-本月累计人次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="15" r="2" rs="2" s="5">
<O>
<![CDATA[1-本月累计金额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="16" r="2" rs="2" s="5">
<O>
<![CDATA[人次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="17" r="2" rs="2" s="5">
<O>
<![CDATA[金额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="18" r="2" rs="2" s="5">
<O>
<![CDATA[人次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="19" r="2" rs="2" s="5">
<O>
<![CDATA[金额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="3" s="5">
<O>
<![CDATA[原因1]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="3" s="5">
<O>
<![CDATA[原因2]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="3" s="5">
<O>
<![CDATA[原因3]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="3" s="5">
<O>
<![CDATA[原因4]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="4" s="7">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="AREA_LEVEL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="4" s="8">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="AREA_CODE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="4" s="9">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="AREA_NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="AreaCode"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B5]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="YearMonth"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$YearMonth]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="FamilyCategoryVal"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$FamilyCategoryVal]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_self]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true">
<![CDATA[/StatisDynamicManage.cpt]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand dir="0"/>
</C>
<C c="4" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="ADD_PEOPLE_COUNT"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="ADD_PEOPLE_FUND"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="ADD_PEOPLE_COUNT_ALL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="ADD_PEOPLE_FUND_ALL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_PEOPLE_COUNT"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_PEOPLE_FUND"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_COUNT_REASON1"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_COUNT_REASON2"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_COUNT_REASON3"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="13" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_COUNT_REASON4"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="14" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_PEOPLE_COUNT_ALL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="15" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="EXIT_PEOPLE_FUND_ALL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="16" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="BC_ADD_PEOPLE_COUNT"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="17" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="BC_ADD_PEOPLE_FUND"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="18" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="BC_EXIT_PEOPLE_COUNT"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="19" r="4" s="4">
<O t="DSColumn">
<Attributes dsName="DynamicManagement" columnName="BC_EXIT_PEOPLE_FUND"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="5" s="10">
<O>
<![CDATA[负责人：]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="5" s="10">
<O>
<![CDATA[审核人：]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="5" s="10">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="5" s="10">
<O>
<![CDATA[填报人：]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="5" s="10">
<PrivilegeControl/>
<Expand/>
</C>
<C c="14" r="5" cs="2" s="10">
<O>
<![CDATA[填报日期：]]></O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting orientation="1">
<PaperSize width="33120000" height="69120000"/>
<Margin top="864000" left="1728000" bottom="864000" right="1728000"/>
</PaperSetting>
<Background name="ColorBackground" color="-1"/>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="false"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="128"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="128"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="120"/>
<Background name="ColorBackground" color="-2171170"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="80"/>
<Background name="ColorBackground" color="-2171170"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="1" size="80"/>
<Background name="ColorBackground" color="-2171170"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72" foreground="-16776961" underline="1"/>
<Background name="NullBackground"/>
<Border>
<Top style="1" color="-16777216"/>
<Bottom style="1" color="-16777216"/>
<Left style="1" color="-16777216"/>
<Right style="1" color="-16777216"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesignerVersion DesignerVersion="KAA"/>
<PreviewType PreviewType="0"/>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="34b8a3ce-8fa5-4f23-a187-0c896bb6835c"/>
</TemplateIdAttMark>
</WorkBook>
