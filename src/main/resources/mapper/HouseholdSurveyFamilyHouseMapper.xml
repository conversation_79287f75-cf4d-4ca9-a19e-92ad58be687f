<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyFamilyHouseMapper">

    <resultMap id="HouseholdSurveyFamilyHouseMap"
               type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyFamilyHouse">
        <result column="id" property="id" javaType="java.lang.String"/>
        <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
        <result column="create_date" property="createDate" javaType="java.util.Date"/>
        <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
        <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
        <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
        <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
        <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
        <result column="household_survey_id" property="householdSurveyId" javaType="java.lang.String"/>
        <result column="property_address" property="propertyAddress" javaType="java.lang.String"/>
        <result column="house_actual_area" property="houseActualArea" javaType="java.math.BigDecimal"/>
        <result column="house_natur_val" property="houseNaturVal" javaType="java.lang.String"/>
        <result column="house_natur_key" property="houseNaturKey" javaType="java.lang.String"/>
        <result column="house_source_val" property="houseSourceVal" javaType="java.lang.String"/>
        <result column="house_source_key" property="houseSourceKey" javaType="java.lang.String"/>
        <result column="buy_house_time" property="buyHouseTime" javaType="java.util.Date"/>
        <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
        <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
        <result column="HOUSE_AREAS" property="houseAreas" javaType="java.math.BigDecimal"/>
        <result column="HOUSE_ID_NUMBER" property="houseIdNumber" javaType="java.lang.String"/>
        <result column="HOUSE_STRUCTURE_VAL" property="houseStructureVal" javaType="java.lang.String"/>
        <result column="HOUSE_STRUCTURE_KEY" property="houseStructureKey" javaType="java.lang.String"/>
    </resultMap>
</mapper>