<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyProjectMapper">

    <resultMap id="HouseholdSurveyProjectMap"
               type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyProject">
        <result column="ID" property="id" javaType="java.lang.String"/>
        <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
        <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
        <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
        <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
        <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
        <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
        <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
        <result column="TYPE_KEY" property="typeKey" javaType="java.lang.String"/>
        <result column="TYPE_VAL" property="typeVal" javaType="java.lang.String"/>
        <result column="NAME" property="name" javaType="java.lang.String"/>
        <result column="SORT_CODE" property="sortCode" javaType="java.lang.Integer"/>
        <result column="STATUS" property="status" javaType="java.lang.Integer"/>
        <result column="REMARK" property="remark" javaType="java.lang.String"/>
        <result column="IS_REQUIRED" property="isRequired" javaType="java.lang.Integer"/>
        <result column="RELATED_TABLE_NAME" property="relatedTableName" javaType="java.lang.String"/>
        <result column="GROUP_VAL" property="groupVal" javaType="java.lang.String"/>
        <result column="GROUP_KEY" property="groupKey" javaType="java.lang.String"/>
    </resultMap>
</mapper>