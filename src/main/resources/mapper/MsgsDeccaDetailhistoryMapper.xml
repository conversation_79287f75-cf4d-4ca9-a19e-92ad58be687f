<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsDeccaDetailhistoryMapper">

    <resultMap id="MsgsDeccaDetailhistoryMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsDeccaDetailhistory">
            <result column="Id" property="id" javaType="java.lang.String"/>
            <result column="Year" property="year" javaType="java.lang.Integer"/>
            <result column="Month" property="month" javaType="java.lang.Integer"/>
            <result column="Family_Id" property="familyId" javaType="java.lang.String"/>
            <result column="County_Code" property="countyCode" javaType="java.lang.String"/>
            <result column="Town_Code" property="townCode" javaType="java.lang.String"/>
            <result column="Village_Code" property="villageCode" javaType="java.lang.String"/>
            <result column="Village_Name" property="villageName" javaType="java.lang.String"/>
            <result column="Number_Code" property="numberCode" javaType="java.lang.String"/>
            <result column="Category_Code" property="categoryCode" javaType="java.lang.String"/>
            <result column="Family_Category_Val" property="familyCategoryVal" javaType="java.lang.String"/>
            <result column="Family_Category_Key" property="familyCategoryKey" javaType="java.lang.String"/>
            <result column="Family_SubCategory_Key" property="familySubcategoryKey" javaType="java.lang.Integer"/>
            <result column="Family_SubCategory_Val" property="familySubcategoryVal" javaType="java.lang.String"/>
            <result column="Apply_Date" property="applyDate" javaType="java.util.Date"/>
            <result column="Current_Start_Date" property="currentStartDate" javaType="java.util.Date"/>
            <result column="I_D_Number" property="iDNumber" javaType="java.lang.String"/>
            <result column="Householder_Id_Card" property="householderIdCard" javaType="java.lang.String"/>
            <result column="Householder" property="householder" javaType="java.lang.String"/>
            <result column="Sex_Code" property="sexCode" javaType="java.lang.Integer"/>
            <result column="Sex_Key" property="sexKey" javaType="java.lang.String"/>
            <result column="Nationality_Key" property="nationalityKey" javaType="java.lang.String"/>
            <result column="Nationality_Val" property="nationalityVal" javaType="java.lang.Integer"/>
            <result column="Family_Address" property="familyAddress" javaType="java.lang.String"/>
            <result column="Person_Count_On_Flow" property="personCountOnFlow" javaType="java.lang.Integer"/>
            <result column="Tel" property="tel" javaType="java.lang.String"/>
            <result column="Is_Three_No_Person" property="isThreeNoPerson" javaType="java.lang.Integer"/>
            <result column="Old_People" property="oldPeople" javaType="java.lang.Integer"/>
            <result column="Adult_People" property="adultPeople" javaType="java.lang.Integer"/>
            <result column="Adult_Working" property="adultWorking" javaType="java.lang.Integer"/>
            <result column="Adultsflexible_Employment" property="adultsflexibleEmployment" javaType="java.lang.Integer"/>
            <result column="Lose_Job_Adult" property="loseJobAdult" javaType="java.lang.Integer"/>
            <result column="Not_Register_Lose_Job" property="notRegisterLoseJob" javaType="java.lang.Integer"/>
            <result column="Have_Labour_Capacity" property="haveLabourCapacity" javaType="java.lang.Integer"/>
            <result column="No_Labour_Capacity" property="noLabourCapacity" javaType="java.lang.Integer"/>
            <result column="Juveniles" property="juveniles" javaType="java.lang.Integer"/>
            <result column="Study_Juveniles" property="studyJuveniles" javaType="java.lang.Integer"/>
            <result column="Woman" property="woman" javaType="java.lang.Integer"/>
            <result column="Juveniles_Other" property="juvenilesOther" javaType="java.lang.Integer"/>
            <result column="Disabled" property="disabled" javaType="java.lang.Integer"/>
            <result column="Severely_Disabled" property="severelyDisabled" javaType="java.lang.Integer"/>
            <result column="Subsidy_Standard" property="subsidyStandard" javaType="java.math.BigDecimal"/>
            <result column="Final_Money" property="finalMoney" javaType="java.math.BigDecimal"/>
            <result column="Bank_Val" property="bankVal" javaType="java.lang.String"/>
            <result column="Bank_Key" property="bankKey" javaType="java.lang.String"/>
            <result column="Bank_Account" property="bankAccount" javaType="java.lang.String"/>
            <result column="House_HolderIsFlow" property="houseHolderisflow" javaType="java.lang.String"/>
            <result column="Classify_Fund" property="classifyFund" javaType="java.math.BigDecimal"/>
            <result column="Relief_Fund" property="reliefFund" javaType="java.math.BigDecimal"/>
            <result column="Raise_Subsidy" property="raiseSubsidy" javaType="java.math.BigDecimal"/>
            <result column="Month_Income" property="monthIncome" javaType="java.math.BigDecimal"/>
            <result column="Person_Avg_Income" property="personAvgIncome" javaType="java.math.BigDecimal"/>
            <result column="All_Care" property="allCare" javaType="java.lang.Integer"/>
            <result column="Part_Provide" property="partProvide" javaType="java.lang.Integer"/>
            <result column="All_Provide" property="allProvide" javaType="java.lang.Integer"/>
            <result column="Poor_Person" property="poorPerson" javaType="java.lang.Integer"/>
            <result column="Member_Name1" property="memberName1" javaType="java.lang.String"/>
            <result column="Member_Name2" property="memberName2" javaType="java.lang.String"/>
            <result column="Member_Name3" property="memberName3" javaType="java.lang.String"/>
            <result column="Member_Name4" property="memberName4" javaType="java.lang.String"/>
            <result column="Member_Name5" property="memberName5" javaType="java.lang.String"/>
            <result column="Member_Name6" property="memberName6" javaType="java.lang.String"/>
            <result column="Member_Name7" property="memberName7" javaType="java.lang.String"/>
            <result column="Member_Name8" property="memberName8" javaType="java.lang.String"/>
            <result column="Member_Name9" property="memberName9" javaType="java.lang.String"/>
            <result column="Member_Card1" property="memberCard1" javaType="java.lang.String"/>
            <result column="Member_Card2" property="memberCard2" javaType="java.lang.String"/>
            <result column="Member_Card3" property="memberCard3" javaType="java.lang.String"/>
            <result column="Member_Card4" property="memberCard4" javaType="java.lang.String"/>
            <result column="Member_Card5" property="memberCard5" javaType="java.lang.String"/>
            <result column="Member_Card6" property="memberCard6" javaType="java.lang.String"/>
            <result column="Member_Card7" property="memberCard7" javaType="java.lang.String"/>
            <result column="Member_Card8" property="memberCard8" javaType="java.lang.String"/>
            <result column="Member_Card9" property="memberCard9" javaType="java.lang.String"/>
            <result column="Delete_Mark" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="Create_Date" property="createDate" javaType="java.util.Date"/>
    </resultMap>
</mapper>