<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsMotorvehiclevesselinfoMapper">

    <resultMap id="MsgsMotorvehiclevesselinfoMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsMotorvehiclevesselinfo">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="FAMILY_ID" property="familyId" javaType="java.lang.String"/>
            <result column="NAME" property="name" javaType="java.lang.String"/>
            <result column="MODEL" property="model" javaType="java.lang.String"/>
            <result column="VEHICLE_NUMBER" property="vehicleNumber" javaType="java.lang.String"/>
            <result column="EXHAUST_CAPACITY" property="exhaustCapacity" javaType="java.lang.String"/>
            <result column="PURCHASE_TIME" property="purchaseTime" javaType="java.util.Date"/>
            <result column="PURCHASE_AMOUNT" property="purchaseAmount" javaType="java.math.BigDecimal"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
    </resultMap>
</mapper>