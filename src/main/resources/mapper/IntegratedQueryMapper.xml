<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.IntegratedQueryMapper">
    <sql id="whereFamily">
        <!-- 入户调查 -->
        <if test="conditions.householdStatus != null">
            AND F.HOUSEHOLD_STATUS = #{conditions.householdStatus}
        </if>
        <if test="conditions.familyCode != null and conditions.familyCode != ''">
            AND F.FAMILY_CODE = #{conditions.familyCode}
        </if>

        <choose>
            <when test="conditions.familyCategoryVal != null and conditions.familyCategoryVal != ''">
                AND F.FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal}
            </when>
            <when test="conditions.forceVal != null and conditions.forceVal != ''">
                AND F.FAMILY_CATEGORY_VAL = #{conditions.forceVal}
            </when>
        </choose>

        <if test="conditions.familySubcategoryVal != null and conditions.familySubcategoryVal != ''">
            AND F.FAMILY_SUBCATEGORY_VAL = #{conditions.familySubcategoryVal}
        </if>

        <if test="conditions.dataStatus != null and conditions.dataStatus != ''">
            AND F.DATA_STATUS = #{conditions.dataStatus}
            <if test="conditions.dataStatus == 1">
                AND F.AUDIT_STATUS = 1
                AND F.FLOW_STATUS = 2
            </if>
        </if>

        <if test="conditions.currentStatus != null and conditions.currentStatus != ''">
            <if test="conditions.currentStatus == 10">
                AND F.DATA_STATUS=0 AND F.OPERATION_CATEGORY=0
            </if>
            <if test="conditions.currentStatus == 20">
                AND F.DATA_STATUS=1
            </if>
            <if test="conditions.currentStatus == 30">
                AND F.DATA_STATUS=0 AND F.OPERATION_CATEGORY=1
            </if>
            <if test="conditions.currentStatus == 40">
                AND F.DATA_STATUS=0 AND F.OPERATION_CATEGORY=3
            </if>
            <if test="conditions.currentStatus == 50">
                AND F.DATA_STATUS=0 AND F.OPERATION_CATEGORY=7
            </if>
            <if test="conditions.currentStatus == 60">
                AND F.DATA_STATUS=5
            </if>
            <if test="conditions.currentStatus == 70">
                AND F.DATA_STATUS=7
            </if>
            <if test="conditions.currentStatus == 80">
                AND F.DATA_STATUS=3
            </if>
            <if test="conditions.currentStatus == 90">
                AND F.DATA_STATUS=6
            </if>
        </if>
        <if test="conditions.operationCategory != null and conditions.operationCategory != ''">
            AND F.OPERATION_CATEGORY = #{conditions.operationCategory}
        </if>
        <if test="conditions.flowStatus != null and conditions.flowStatus != ''">
            AND F.FLOW_STATUS = #{conditions.flowStatus}
        </if>

        <if test="conditions.checkState != null and conditions.checkState != ''">
            AND F.CHECK_STATE = #{conditions.checkState}
        </if>
        <if test="conditions.isWarning != null and conditions.isWarning != ''">
            AND F.IS_WARNING = #{conditions.isWarning}
        </if>
        <if test="conditions.startApplyDate != null and conditions.startApplyDate != ''">
            AND F.APPLY_DATE >= TO_DATE(#{conditions.startApplyDate}, 'YYYY-MM-DD')
        </if>
        <if test="conditions.endApplyDate != null and conditions.endApplyDate != ''">
            AND F.APPLY_DATE <![CDATA[ <= ]]> TO_DATE(#{conditions.endApplyDate}, 'YYYY-MM-DD')
        </if>
        <if test="conditions.startFirstStartDate != null and conditions.startFirstStartDate != ''">
            AND F.CURRENT_START_DATE >= TO_DATE(#{conditions.startFirstStartDate} || '-01', 'YYYY-MM-DD')
        </if>
        <if test="conditions.endFirstStartDate != null and conditions.endFirstStartDate != ''">
            AND F.CURRENT_START_DATE <![CDATA[ < ]]> ADD_MONTHS(TO_DATE(#{conditions.endFirstStartDate} ||
            '-01','YYYY-MM-DD'), 1)
        </if>
        <if test="conditions.povertyCauseVal != null and conditions.povertyCauseVal != ''">
            AND F.POVERTY_CAUSE_VAL = #{conditions.povertyCauseVal}
        </if>
        <if test="conditions.startEndDate != null and conditions.startEndDate != ''">
            AND F.END_DATE >= TO_DATE(#{conditions.startEndDate}, 'YYYY-MM-DD')
        </if>
        <if test="conditions.endEndDate != null and conditions.endEndDate != ''">
            AND F.END_DATE <![CDATA[ <= ]]> TO_DATE(#{conditions.endEndDate}, 'YYYY-MM-DD')
        </if>
        <if test="conditions.operationReason != null and conditions.operationReason != ''">
            AND F.OPERATION_REASON = #{conditions.operationReason}
        </if>
        <if test="conditions.singleInsuranceVal != null and conditions.singleInsuranceVal != ''">
            AND F.SINGLE_INSURANCE_VAL = #{conditions.singleInsuranceVal}
        </if>
        <if test="conditions.reviewStatus != null and conditions.reviewStatus != ''">
            AND F.REVIEW_STATUS = #{conditions.reviewStatus}
        </if>
        <include refid="hdsqsWhere"/>
        <include refid="memberWhere"/>
    </sql>

    <sql id="fieldFamily">
        SELECT F.ID,
               F.FAMILY_CODE,
               F.HOUSEHOLDER,
               F.HOUSEHOLDER_ID_CARD,
               F.FAMILY_CATEGORY_VAL,
               F.FAMILY_SUBCATEGORY_VAL,
               F.FAMILY_SUBCATEGORY_KEY,
               CASE
                   WHEN F.DATA_STATUS = 0 AND F.OPERATION_CATEGORY = 0 THEN 10
                   WHEN F.DATA_STATUS = 1 THEN 20
                   WHEN F.DATA_STATUS = 0 AND F.OPERATION_CATEGORY = 1 THEN 30
                   WHEN F.DATA_STATUS = 0 AND F.OPERATION_CATEGORY = 3 THEN 40
                   WHEN F.DATA_STATUS = 0 AND F.OPERATION_CATEGORY = 7 THEN 50
                   WHEN F.DATA_STATUS = 5 THEN 60
                   WHEN F.DATA_STATUS = 7 THEN 70
                   WHEN F.DATA_STATUS = 3 THEN 80
                   when F.DATA_STATUS = 6 then 90 END                 AS CURRENT_STATUS,
               NVL(F.CITY_NAME, '') || '-' || NVL(F.COUNTY_NAME, '') || '-' ||
               NVL(F.TOWN_NAME, '') || '-' || NVL(F.VILLAGE_NAME, '') AS AREAINFO,
               NVL(F.CHECK_STATE, 0)                                  AS CHECK_STATE,
               NVL(F.HOUSEHOLD_STATUS, 0)                             AS HOUSEHOLD_STATUS,
               NVL(F.IS_WARNING, 0)                                   AS IS_WARNING,
               F.PERSON_COUNT,
               F.PERSON_COUNT_ON_FLOW,
               F.RELIEF_FUND,
               F.CLASSIFY_FUND,
               F.TOTAL_FUND,
               F.APPLY_DATE,
               F.CURRENT_START_DATE                                   AS FIRST_START_DATE,
               F.MODIFY_DATE,
               F.END_DATE,
               F.DATA_STATUS,
               F.OPERATION_CATEGORY,
               F.FLOW_STATUS,
               F.REVIEW_STATUS,
               F.SINGLE_INSURANCE_VAL,
               F.OPERATION_REASON,
               #{conditions.hasHdsqs}                                 AS HAS_HDSQS,
               NULL                                                   AS PERSONS_NAME,
               F.FLOW_INSTANCE_ID

    </sql>

    <select id="selectFamilyInfomationGroup"
            resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.FamilyInfomationGroupVO">
        SELECT
        F.${conditions.areaName} AS areaName,
        F.${conditions.areaCode} AS areaCode,
        F.FAMILY_CATEGORY_VAL AS bzlx,
        F.FAMILY_SUBCATEGORY_VAL AS subbzlx,
        COUNT(F.ID) AS bzjt,
        SUM(F.PERSON_COUNT) AS jtcys,
        SUM(F.PERSON_COUNT_ON_FLOW) AS bzrs,
        SUM(F.TOTAL_FUND) AS bzje
        FROM ${databaseName}.${conditions.familyTableName} F
        WHERE F.DELETE_MARK = 0

        <!-- 入户调查 -->
        <if test="conditions.householdStatus != null">
            AND F.HOUSEHOLD_STATUS = #{conditions.householdStatus}
        </if>

        <!-- 家庭类别，优先使用 familyCategoryVal -->
        <choose>
            <when test="conditions.familyCategoryVal != null and conditions.familyCategoryVal != ''">
                AND F.FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal}
            </when>
            <when test="conditions.forceVal != null and conditions.forceVal != ''">
                AND F.FAMILY_CATEGORY_VAL = #{conditions.forceVal}
            </when>
        </choose>

        <!-- 区域层级条件 -->
        AND F.${conditions.areaQueryCondition}

        <!-- 家庭子类 -->
        <if test="conditions.familySubcategoryVal != null and conditions.familySubcategoryVal != ''">
            AND F.FAMILY_SUBCATEGORY_VAL = #{conditions.familySubcategoryVal}
        </if>

        <!-- 数据状态 -->
        <if test="conditions.dataStatus != null and conditions.dataStatus != ''">
            AND F.DATA_STATUS = #{conditions.dataStatus}
            <if test="conditions.dataStatus == 1">
                AND F.AUDIT_STATUS = 1
                AND F.FLOW_STATUS = 2
            </if>
        </if>

        GROUP BY
        F.${conditions.areaName},
        F.${conditions.areaCode},
        F.FAMILY_CATEGORY_VAL,
        F.FAMILY_SUBCATEGORY_VAL
        ORDER BY
        F.${conditions.areaCode}
    </select>

    <select id="selectFamilyInfoDetailsFast" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsFamilyinfoVO">
        <include refid="fieldFamily"/>
        FROM ${databaseName}.${conditions.familyTableName} F
        where F.DELETE_MARK = 0
        AND F.${conditions.areaQueryCondition}
        <include refid="whereFamily"/>
        ORDER BY F.APPLY_DATE DESC
    </select>

    <select id="selectFamilyInfomationGroupTotal"
            resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.FamilyInfomationGroupTotalVO">
        SELECT
        COUNT(F.ID) AS bzjtTotal,
        SUM(F.PERSON_COUNT) AS rsTotal,
        SUM(F.PERSON_COUNT_ON_FLOW) AS bzrsTotal,
        SUM(F.TOTAL_FUND) AS bzjeTotal,
        SUM(F.RELIEF_FUND) AS cejeTotal,
        SUM(F.CLASSIFY_FUND) AS fljeTotal,
        F.${conditions.areaName} as areaName,
        F.${conditions.areaCode} as areaCode
        <if test="conditions.familySubcategoryVal != null and conditions.familySubcategoryVal != ''">
            , F.FAMILY_SUBCATEGORY_VAL
        </if>
        FROM ${databaseName}.${conditions.familyTableName} F
        WHERE F.DELETE_MARK = 0
        AND F.${conditions.areaQueryCondition}
        <include refid="whereFamily"/>
        GROUP BY
        F.${conditions.areaName},F.${conditions.areaCode}
        <if test="conditions.familySubcategoryVal != null and conditions.familySubcategoryVal != ''">
            , F.FAMILY_SUBCATEGORY_VAL
        </if>
    </select>

    <sql id="wherePerson">
        AND T2.${conditions.areaQueryCondition}
        <if test="conditions.dataStatus !=null and conditions.dataStatus !=''">
            and T2.DATA_STATUS = #{conditions.dataStatus}
            <if test="conditions.dataStatus == 1 ">
                AND T2.AUDIT_STATUS = 1
                AND T2.FLOW_STATUS = 2
            </if>
        </if>
        <if test="conditions.personIdCard !=null and conditions.personIdCard !=''">
            AND T1.PERSON_ID_CARD = #{conditions.personIdCard}
        </if>
        <if test="conditions.personName !=null and conditions.personName !=''">
            AND T1.PERSON_NAME = #{conditions.personName}
        </if>
        <if test="conditions.familyRelationVal !=null and conditions.familyRelationVal !=''">
            and T1.FAMILY_RELATION_VAL = #{conditions.familyRelationVal}
        </if>
        <if test="conditions.sex !=null and conditions.sex !=''">
            and T1.SEX = #{conditions.sex}
        </if>
        <if test="conditions.isSingleInsurance !=null and conditions.isSingleInsurance !=''">
            and T1.IS_SINGLE_INSURANCE = #{conditions.isSingleInsurance}
        </if>
        <if test="conditions.singleInsuranceMemberTypeVal !=null and conditions.singleInsuranceMemberTypeVal !=''">
            and T1.SINGLE_INSURANCE_MEMBER_TYPE_VAL= #{conditions.singleInsuranceMemberTypeVal}
        </if>
        <if test="conditions.singleInsuranceCategoryVal !=null and conditions.singleInsuranceCategoryVal !=''">
            and T1.SINGLE_INSURANCE_CATEGORY_VAL= #{conditions.singleInsuranceCategoryVal}
        </if>
        <if test="conditions.singleTypeVal !=null and conditions.singleTypeVal !=''">
            and T2.SINGLE_TYPE_VAL = #{conditions.singleTypeVal}
        </if>
        <if test="conditions.classifyCategoryVal !=null and conditions.classifyCategoryVal !=''">
            and T1.CLASSIFY_CATEGORY_VAL = #{conditions.classifyCategoryVal}
        </if>
        <if test="conditions.familyNatureVal !=null and conditions.familyNatureVal !=''">
            and T1.FAMILY_NATURE_VAL = #{conditions.familyNatureVal}
        </if>
        <if test="conditions.personCategoryVal !=null and conditions.personCategoryVal !=''">
            and T1.PERSON_CATEGORY_VAL = #{conditions.personCategoryVal}
        </if>
        <if test="conditions.nationalityVal !=null and conditions.nationalityVal !=''">
            and T1.IS_INDEMNIFY_PERSON = #{conditions.nationalityVal}
        </if>
        <if test="conditions.disabilityCategoryVal!=null and conditions.disabilityCategoryVal.size>0">
            and T1.DISABILITY_CATEGORY_VAL in
            <foreach collection="conditions.disabilityCategoryVal" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="conditions.disabilityCategoryLevelVal!=null and conditions.disabilityCategoryLevelVal.size>0">
            and T1.DISABILITY_CATEGORY_LEVEL_VAL in
            <foreach collection="conditions.disabilityCategoryLevelVal" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="conditions.isSeriously !=null and conditions.isSeriously !=''">
            and T1.IS_SERIOUSLY = #{conditions.isSeriously}
        </if>
        <if test="conditions.isSevereDisability !=null and conditions.isSevereDisability !=''">
            and T1.IS_SEVERE_DISABILITY = #{conditions.isSevereDisability}
        </if>
        <if test="conditions.selfCareAbilityVal !=null and conditions.selfCareAbilityVal !=''">
            and T1.SELF_CARE_ABILITY_VAL = #{conditions.selfCareAbilityVal}
        </if>

        <if test="conditions.povertyTypeVal !=null and conditions.povertyTypeVal !=''">
            and T1.POVERTY_TYPE_VAL = #{conditions.povertyTypeVal}
        </if>
        <if test="conditions.monitorTypeVal !=null and conditions.monitorTypeVal !=''">
            and T1.MONITOR_TYPE_VAL = #{conditions.monitorTypeVal}
        </if>
        <if test="conditions.isIndemnifyPerson !=null and conditions.isIndemnifyPerson !=''">
            and T1.IS_INDEMNIFY_PERSON = #{conditions.isIndemnifyPerson}
        </if>
        <if test="conditions.particularCategoryVal !=null and conditions.particularCategoryVal !=''">
            and T1.PARTICULAR_CATEGORY_VAL = #{conditions.particularCategoryVal}
        </if>
        <if test="conditions.ageStart !=null and conditions.ageStart !=''">
            and TIMESTAMPDIFF(MONTH, T1.BIRTHDAY, CURRENT_DATE) / 12 <![CDATA[ >= ]]> #{conditions.ageStart}
        </if>
        <if test="conditions.ageEnd !=null and conditions.ageEnd !=''">
            and TIMESTAMPDIFF(MONTH, T1.BIRTHDAY, CURRENT_DATE) / 12 <![CDATA[ <= ]]> #{conditions.ageEnd}
        </if>

        <if test="null != conditions.startFirstStartDate and '' != conditions.startFirstStartDate">
            AND DATE_FORMAT(T1.FIRST_START_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{conditions.startFirstStartDate}
        </if>
        <if test="null != conditions.endFirstStartDate and '' != conditions.endFirstStartDate">
            AND DATE_FORMAT(T1.FIRST_START_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{conditions.endFirstStartDate}
        </if>
        <if test="null != conditions.startTransferDate and '' != conditions.startTransferDate">
            AND DATE_FORMAT(T2.MODIFY_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{conditions.startTransferDate}
        </if>
        <if test="null != conditions.endTransferDate and '' != conditions.endTransferDate">
            AND DATE_FORMAT(T2.MODIFY_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{conditions.endTransferDate}
        </if>
        <if test="null != conditions.startEndDate and '' != conditions.startEndDate">
            AND DATE_FORMAT(T1.END_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{conditions.startEndDate}
        </if>
        <if test="null != conditions.endEndDate and '' != conditions.endEndDate">
            AND DATE_FORMAT(T1.END_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{conditions.endEndDate}
        </if>

        <if test="conditions.operationCategory != null and conditions.operationCategory != ''">
            AND T2.OPERATION_CATEGORY = #{conditions.operationCategory}
        </if>
        <if test="conditions.flowStatus != null and conditions.flowStatus != ''">
            AND T2.FLOW_STATUS = #{conditions.flowStatus}
        </if>
        <if test="conditions.deleteReasonVal != null and conditions.deleteReasonVal != ''">
            AND T1.DELETE_REASON_VAL = #{conditions.deleteReasonVal}
        </if>

    </sql>

    <sql id="fieldPerson">
        SELECT
            T1.FIRST_START_DATE,
            T1.SELF_CARE_ABILITY_VAL,
            T1.SELF_CARE_ABILITY_KEY,
            T1.PERSON_PHONE_NUMBER,
            T1.IS_POVERTY_BY_ILLNESS,
            T1.IS_SINGLE_INSURANCE,
            T1.SINGLE_INSURANCE_MEMBER_TYPE_VAL,
            T1.SINGLE_INSURANCE_MEMBER_TYPE_KEY,
            T1.SINGLE_INSURANCE_CATEGORY_VAL,
            T1.SINGLE_INSURANCE_CATEGORY_KEY,
            T2.SINGLE_TYPE_VAL,
            T2.SINGLE_TYPE_KEY,
            T1.NATIONALITY_VAL,
            T1.NATIONALITY_KEY,
            T1.FAMILY_NATURE_VAL,
            T1.FAMILY_NATURE_KEY,
            T1.CLASSIFY_CATEGORY_VAL,
            T1.CLASSIFY_CATEGORY_KEY,
            T1.IS_SPECIAL_PERSON,
            T1.PERSON_CATEGORY_VAL,
            T1.PERSON_CATEGORY_KEY,
            T1.FAMILY_CODE,
            T1.BIRTHDAY,
            T1.SEX,T1.DISABILITY_CATEGORY_VAL,T1.DISABILITY_CATEGORY_KEY,T1.DISABILITY_CATEGORY_LEVEL_VAL,T1.DISABILITY_CATEGORY_LEVEL_KEY,
            T1.IS_SERIOUSLY,T1.IS_SEVERE_DISABILITY,T1.POVERTY_TYPE_VAL,T1.POVERTY_TYPE_KEY,T1.MONITOR_TYPE_VAL,T1.MONITOR_TYPE_KEY,
            T1.FAMILY_RELATION_VAL,
            T1.FAMILY_RELATION_KEY,
            T1.IS_INDEMNIFY_PERSON,
            T1.PARTICULAR_CATEGORY_VAL,
            T1.PARTICULAR_CATEGORY_KEY,
            T1.PERSON_CODE,
            T1.PERSON_NAME,
            T1.PERSON_ID_CARD,
            T2.CITY_NAME || '-' || T2.COUNTY_NAME || '-' || T2.TOWN_NAME || '-' || T2.VILLAGE_NAME AS AREAINFO,
            T2.FAMILY_CATEGORY_VAL,
            T2.FAMILY_CATEGORY_KEY,
            CASE T2.REVIEW_STATUS
                WHEN 0 THEN '正常数据'
                WHEN 1 THEN '正在复查'
                WHEN 3 THEN '正在注销'
                WHEN 5 THEN '正在迁移'
                WHEN 6 THEN '正在转其他类型'
                ELSE '-'
                END AS CURRENT_STATUS,
            TIMESTAMPDIFF(MONTH, T1.BIRTHDAY, CURRENT_DATE) / 12 AS AGE,
            TO_CHAR(T1.BIRTHDAY, 'YYYY-MM-DD') AS BIRTHDAYS,
            T2.APPLY_DATE,
            T2.DATA_STATUS,
            T2.OPERATION_CATEGORY,
            T2.OPERATION_REASON,
            T2.FLOW_STATUS,T1.ID,T1.FAMILY_ID,T2.HOUSEHOLDER,T2.HOUSEHOLDER_ID_CARD,
            T1.DELETE_REASON_KEY,T1.DELETE_REASON_VAL,T1.END_DATE,T2.MODIFY_DATE as transferDate
    </sql>
    <select id="selectPersonInfo" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsPersonInfoVO">
        <include refid="fieldPerson"/>
        ,T2.FAMILY_SUBCATEGORY_VAL
        ,T2.FAMILY_SUBCATEGORY_KEY
        FROM ${databaseName}.${conditions.memberTableName} T1
        INNER JOIN ${databaseName}.${conditions.familyTableName} T2
        ON T1.FAMILY_ID = T2.ID
        WHERE T1.DELETE_MARK = 0 AND T2.DELETE_MARK = 0
        <if test="conditions.familyCategoryVal != ''">
            AND T2.FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal}
        </if>
        <if test="conditions.familySubcategoryVal != null and conditions.familySubcategoryVal != ''">
            AND T2.FAMILY_SUBCATEGORY_VAL = #{conditions.familySubcategoryVal}
        </if>
        <include refid="wherePerson"/>
        ORDER BY
        T2.APPLY_DATE DESC
    </select>

    <select id="selectDsrPersonInfo" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsPersonInfoVO">
        <include refid="fieldPerson"/>
        ,case when #{conditions.familyCategoryVal}='04' then DECODE(T2.FAMILY_CATEGORY_VAL, '01', '0406', '02', '0405', T2.FAMILY_SUBCATEGORY_VAL)
         else DECODE(T2.FAMILY_CATEGORY_VAL, '01', '0501', '02', '0502', T2.FAMILY_SUBCATEGORY_VAL) end AS FAMILY_SUBCATEGORY_VAL
        ,case when #{conditions.familyCategoryVal}='04' then DECODE(T2.FAMILY_CATEGORY_VAL, '01', '农村低保边缘', '02', '城市低保边缘', T2.FAMILY_SUBCATEGORY_KEY)
        else DECODE(T2.FAMILY_CATEGORY_VAL, '01', '农村支出型困难家庭', '02', '城市支出型困难家庭', T2.FAMILY_SUBCATEGORY_KEY) end AS FAMILY_SUBCATEGORY_KEY
        ,case when #{conditions.familyCategoryVal}='04' then DECODE(T2.FAMILY_CATEGORY_VAL, '04', '低保边缘家庭成员', '单人保家庭中的低边人口')
        else DECODE(T2.FAMILY_CATEGORY_VAL, '05', '刚性支出家庭成员', '单人保家庭中的刚性支出人口') end AS lowCategoryName
        ,case when #{conditions.familyCategoryVal}='04' then DECODE(T2.FAMILY_CATEGORY_VAL, '04', '1', '2')
        else DECODE(T2.FAMILY_CATEGORY_VAL, '05', '1', '2') end AS lowCategory
        FROM ${databaseName}.${conditions.familyTableName} T2
        INNER JOIN ${databaseName}.${conditions.memberTableName} T1
        ON T2.ID = T1.FAMILY_ID
        WHERE T2.DELETE_MARK = 0 AND T1.DELETE_MARK = 0

        <if test="conditions.lowCategory ==null or conditions.lowCategory ==''">
            AND (
            (T2.FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal})
            OR
            (T2.FAMILY_CATEGORY_VAL IN ('01','02')
            AND T2.IS_SINGLE_INSURANCE = 1 AND t2.SINGLE_TYPE_VAL= #{conditions.singleType}
            AND T1.IS_INDEMNIFY_PERSON = 0)
            )
        </if>
        <if test="conditions.lowCategory !=null or conditions.lowCategory =='1'">
            AND T2.FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal}
        </if>
        <if test="conditions.lowCategory !=null or conditions.lowCategory =='2'">
            AND (T2.FAMILY_CATEGORY_VAL IN ('01','02')
            AND T2.IS_SINGLE_INSURANCE = 1 AND t2.SINGLE_TYPE_VAL= #{conditions.singleType}
            AND T1.IS_INDEMNIFY_PERSON = 0)
        </if>

        <if test="conditions.familySubcategoryVal !=null and (conditions.familySubcategoryVal =='0406' or conditions.familySubcategoryVal =='0501')">
            AND (T2.FAMILY_SUBCATEGORY_VAL = #{conditions.familySubcategoryVal} OR T2.FAMILY_CATEGORY_VAL = '01')
        </if>
        <if test="conditions.familySubcategoryVal !=null and (conditions.familySubcategoryVal =='0405' or conditions.familySubcategoryVal =='0502')">
            AND (T2.FAMILY_SUBCATEGORY_VAL = #{conditions.familySubcategoryVal} OR T2.FAMILY_CATEGORY_VAL = '02')
        </if>

        <include refid="wherePerson"/>
        ORDER BY T2.APPLY_DATE DESC
    </select>

    <sql id="whereRoster">
        AND T2.${conditions.areaQueryCondition}
        <if test="conditions.personIdCard !=null and conditions.personIdCard !=''">
            AND T3.PERSON_ID_CARD = #{conditions.personIdCard}
        </if>
        <if test="conditions.personName !=null and conditions.personName !=''">
            AND T3.PERSON_NAME = #{conditions.personName}
        </if>
        <if test="conditions.sex !=null and conditions.sex !=''">
            and T3.SEX = #{conditions.sex}
        </if>
        <if test="conditions.familyRelationVal !=null and conditions.familyRelationVal !=''">
            and T3.FAMILY_RELATION_VAL = #{conditions.familyRelationVal}
        </if>
        <if test="conditions.houseHolderName !=null and conditions.houseHolderName !=''">
            and T2.HOUSEHOLDER = #{conditions.houseHolderName}
        </if>
        <!-- 入户调查 -->
        <if test="conditions.householdStatus != null">
            AND T2.HOUSEHOLD_STATUS = #{conditions.householdStatus}
        </if>
        <if test="conditions.personCategoryVal !=null and conditions.personCategoryVal !=''">
            and T3.PERSON_CATEGORY_VAL = #{conditions.personCategoryVal}
        </if>
        <if test="conditions.politicsStatusVal !=null and conditions.politicsStatusVal !=''">
            and T3.POLITICS_STATUS_VAL = #{conditions.politicsStatusVal}
        </if>
        <if test="conditions.personTypeVal !=null and conditions.personTypeVal !=''">
            and T3.PERSON_TYPE_VAL = #{conditions.personTypeVal}
        </if>
        <if test="conditions.familyNatureVal !=null and conditions.familyNatureVal !=''">
            and T3.FAMILY_NATURE_VAL = #{conditions.familyNatureVal}
        </if>
        <if test="conditions.isSpecialPerson !=null and conditions.isSpecialPerson !=''">
            and T3.IS_SPECIAL_PERSON = #{conditions.isSpecialPerson}
        </if>
        <if test="conditions.selfCareAbilityVal !=null and conditions.selfCareAbilityVal !=''">
            and T3.SELF_CARE_ABILITY_VAL = #{conditions.selfCareAbilityVal}
        </if>
        <if test="conditions.particularCategoryVal !=null and conditions.particularCategoryVal !=''">
            and T3.PARTICULAR_CATEGORY_VAL = #{conditions.particularCategoryVal}
        </if>
        <if test="conditions.workStatusVal !=null and conditions.workStatusVal !=''">
            and T3.WORK_STATUS_VAL = #{conditions.workStatusVal}
        </if>
        <if test="conditions.educationalLevelVal !=null and conditions.educationalLevelVal !=''">
            and T3.EDUCATIONAL_LEVEL_VAL = #{conditions.educationalLevelVal}
        </if>
        <if test="conditions.isSevereDisability !=null and conditions.isSevereDisability !='' or conditions.isSevereDisability == 0">
            and T3.IS_SEVERE_DISABILITY = #{conditions.isSevereDisability}
        </if>
        <if test="conditions.classifyCategoryVal !=null and conditions.classifyCategoryVal !=''">
            and T3.CLASSIFY_CATEGORY_VAL = #{conditions.classifyCategoryVal}
        </if>
        <if test="null != conditions.startApplyDate and '' != conditions.startApplyDate">
            AND DATE_FORMAT(T2.APPLY_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{conditions.startApplyDate}
        </if>
        <if test="null != conditions.endApplyDate and '' != conditions.endApplyDate">
            AND DATE_FORMAT(T2.APPLY_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{conditions.endApplyDate}
        </if>
        <if test="null != conditions.startFirstStartDate and '' != conditions.startFirstStartDate">
            AND DATE_FORMAT(T2.CURRENT_START_DATE, '%Y-%m') <![CDATA[ >= ]]> #{conditions.startFirstStartDate}
        </if>
        <if test="null != conditions.endFirstStartDate and '' != conditions.endFirstStartDate">
            AND DATE_FORMAT(T2.CURRENT_START_DATE, '%Y-%m') <![CDATA[ <= ]]> #{conditions.endFirstStartDate}
        </if>
        <if test="null != conditions.startStartDate and '' != conditions.startStartDate">
            AND DATE_FORMAT(T1.START_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{conditions.startStartDate}
        </if>
        <if test="null != conditions.endStartDate and '' != conditions.endStartDate">
            AND DATE_FORMAT(T1.START_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{conditions.endStartDate}
        </if>
        <if test="conditions.isSeriously !=null and conditions.isSeriously !=''">
            and T3.IS_SERIOUSLY = #{conditions.isSeriously}
        </if>
        <if test="conditions.isDisabled !=null and conditions.isDisabled !=''">
            and T3.IS_DISABLED = #{conditions.isSevereDisability}
        </if>
        <if test="conditions.operationCategory !=null">
            and T1.OPERATION_CATEGORY = #{conditions.operationCategory}
        </if>
    </sql>

    <sql id="fieldRoster">
        SELECT T2.CITY_NAME || '-' || T2.COUNTY_NAME || '-' || T2.TOWN_NAME || '-' || T2.VILLAGE_NAME AS AREAINFO,
               T2.HOUSEHOLDER,
               T1.FAMILY_ID,
               T2.FAMILY_CODE,
               T2.HOUSEHOLDER_ID_CARD,
               T2.FAMILY_CATEGORY_VAL,
               T3.PERSON_NAME,
               T3.PERSON_ID_CARD,
               T3.FAMILY_NATURE_VAL,
               T3.SEX,
               DATEDIFF(MONTH , TO_CHAR(T3.BIRTHDAY, 'YYYY-MM-DD'), CURDATE()) / 12                   AS AGE,
               DATE_FORMAT(T3.BIRTHDAY, '%Y-%m-%d')                                                   AS BIRTHDAY,
               T3.IS_INDEMNIFY_PERSON,
               T3.MARITAL_STATUS_VAL,
               T3.DISABILITY_CATEGORY_VAL,
               T3.DISABILITY_CATEGORY_LEVEL_VAL,
               T3.TOTAL_FUND,
               T3.CLASSIFY_FUND,
               T3.PHOTO_PATH,
               T3.IS_STILL_DIFFICULT,
               T2.TEL                                                                                 AS PERSON_PHONE_NUMBER,
               T3.ID                                                                                  AS personId,
               T1.START_DATE,
               DECODE(T1.OPERATION_CATEGORY, 0, '整户新增', 1, '复查', 2, '停发', 3, '整户注销', 4, '恢复', 5,
                      '迁移')                                                                         AS ADDTYPE,
               T3.FAMILY_RELATION_VAL
        FROM ${databaseName}.MSGS_CHANGEHISTORYINFO T1
                 INNER JOIN ${databaseName}.MSGS_FAMILYINFOHISTORY T2 ON
            T1.FAMILY_ID = T2.ID
                 INNER JOIN ${databaseName}.MSGS_FAMILYMEMBERINFOHISTORY T3 ON
            T2.ID = T3.FAMILY_ID
        WHERE T1.DELETE_MARK = 0
          AND T2.DELETE_MARK = 0
          AND T3.DELETE_MARK = 0
    </sql>

    <sql id="hdsqsWhere">
        <!-- 附件过滤 -->
        <if test="conditions.hasHdsqs != null">
            AND ${conditions.hasHdsqs == 1 || conditions.hasHdsqs == '1' ? 'EXISTS' : 'NOT EXISTS'} (
            SELECT 1
            FROM ${databaseName}.MSGS_ATTACHMENTINFO ATT
            WHERE ATT.OBJECT_ID = ${conditions.familyPrefix}.ID
            AND ATT.DELETE_MARK = 0
            <if test="conditions.authorizationLetter != null and conditions.authorizationLetter != ''">
                AND ATT.FILE_CATEGORY = #{conditions.authorizationLetter}
            </if>
            )
        </if>
    </sql>

    <sql id="memberWhere">
        <!-- 成员相关过滤合并 -->
        <if test="(conditions.personIdCard != null and conditions.personIdCard != '')
            or (conditions.personName != null and conditions.personName != '')">
            AND EXISTS (
            SELECT 1
            FROM ${databaseName}.${conditions.memberTableName} M
            WHERE M.FAMILY_ID = ${conditions.familyPrefix}.ID
            AND M.DELETE_MARK = 0
            <if test="conditions.personIdCard != null and conditions.personIdCard != ''">
                AND M.PERSON_ID_CARD = #{conditions.personIdCard}
            </if>
            <if test="conditions.personName != null and conditions.personName != ''">
                AND M.PERSON_NAME = #{conditions.personName}
            </if>
            )
        </if>
    </sql>

    <select id="personnelRosterList" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.SearchParamsVO">
        <include refid="fieldRoster"/>

        AND T3.IS_INDEMNIFY_PERSON = 1
        <if test="conditions.familyCategoryVal != ''">
            AND T2.FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal}
        </if>
        <if test="conditions.operationType == 1">
            AND T3.OPERATION_TYPE in(1,3)
        </if>
        <if test="conditions.operationType == 2">
            AND T3.OPERATION_TYPE in(2,4)
        </if>
        <include refid="whereRoster"/>
        ORDER BY T2.APPLY_DATE DESC
    </select>

    <select id="dsrPersonnelRosterList" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.SearchParamsVO">
        <include refid="fieldRoster"/>
        <if test="conditions.operationType == 1">
            AND ((T2.FAMILY_CATEGORY_VAL ='04' and T3.OPERATION_TYPE=1 ) or (T2.FAMILY_CATEGORY_VAL in('01','02') and
            T3.OPERATION_TYPE in(4,5) ))
        </if>
        <if test="conditions.operationType == 2">
            AND ((T2.FAMILY_CATEGORY_VAL ='04' and T3.OPERATION_TYPE=2 ) or (T2.FAMILY_CATEGORY_VAL in('01','02') and
            T3.OPERATION_TYPE in(3,6) ))
        </if>
        <include refid="whereRoster"/>
        ORDER BY T2.APPLY_DATE DESC
    </select>



    <select id="selectPageFamilyInfo" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.FamilyInfomationGroupVO">
        SELECT
        t.areaName,
        t.areaCode,
        t.FAMILY_CATEGORY_VAL AS bzlx,
        t.FAMILY_SUBCATEGORY_VAL AS subbzlx,
        COUNT(t.id) AS bzjt,
        SUM(t.PERSON_COUNT) AS jtcys,
        SUM(t.PERSON_COUNT_ON_FLOW) AS bzrs,
        SUM(t.TOTAL_FUND) AS bzje
        FROM (
        SELECT
        ${conditions.areaName} as areaName,
        ${conditions.areaCode} as areaCode,
        FAMILY_CATEGORY_VAL,
        FAMILY_SUBCATEGORY_VAL,
        id,
        PERSON_COUNT,
        PERSON_COUNT_ON_FLOW,
        TOTAL_FUND
        FROM ${databaseName}.${conditions.familyTableName}
        WHERE
        DELETE_MARK = 0
        AND FAMILY_CATEGORY_VAL = #{conditions.familyCategoryVal}
        <if test="conditions.familySubcategoryVal != null and conditions.familySubcategoryVal != ''">
            AND FAMILY_SUBCATEGORY_VAL = #{conditions.familySubcategoryVal}
        </if>
        <!-- 入户调查 -->
        <if test="conditions.householdStatus != null">
            AND HOUSEHOLD_STATUS = #{conditions.householdStatus}
        </if>
        AND ${conditions.areaQueryCondition}
        <if test="conditions.dataStatus != null and conditions.dataStatus != ''">
            AND DATA_STATUS = #{conditions.dataStatus}
            <if test="conditions.dataStatus == 1">
                AND AUDIT_STATUS = 1
                AND FLOW_STATUS = 2
            </if>
        </if>
        ) t
        GROUP BY
        t.areaCode,
        t.areaName,
        t.FAMILY_CATEGORY_VAL,
        t.FAMILY_SUBCATEGORY_VAL
        ORDER BY
        t.areaCode DESC
    </select>

    <select id="selectFamilyInfoGroupTotal"
            resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.FamilyInfomationGroupTotalVO">
        SELECT
        COUNT(t.id) AS bzjtTotal,
        SUM(t.PERSON_COUNT) AS rsTotal,
        SUM(t.PERSON_COUNT_ON_FLOW) AS bzrsTotal,
        SUM(t.TOTAL_FUND) AS bzjeTotal,
        SUM(t.RELIEF_FUND) AS cejeTotal,
        SUM(t.CLASSIFY_FUND) AS fljeTotal,
        FROM (
        SELECT
        F.${conditions.areaName} as areaName,
        F.${conditions.areaCode} as areaCode
        <if test="familyInfoGroupTotalVo.familySubcategoryVal != null and familyInfoGroupTotalVo.familySubcategoryVal != ''">
            , F.FAMILY_SUBCATEGORY_VAL
        </if>
        , F.ID
        , F.PERSON_COUNT
        , F.PERSON_COUNT_ON_FLOW
        , F.TOTAL_FUND,
        , F.RELIEF_FUND , F.CLASSIFY_FUND
        FROM ${databaseName}.${conditions.familyTableName} F
        WHERE F.DELETE_MARK = 0

        <!-- 区域层级查询 -->
        AND F.${conditions.areaQueryCondition}

        <!-- 家庭类别条件（优先 familyCategoryVal，其次 forceVal） -->
        <choose>
            <when test="familyInfoGroupTotalVo.familyCategoryVal != null and familyInfoGroupTotalVo.familyCategoryVal != ''">
                AND F.FAMILY_CATEGORY_VAL = #{familyInfoGroupTotalVo.familyCategoryVal}
            </when>
            <when test="conditions.forceVal != null and conditions.forceVal != ''">
                AND F.FAMILY_CATEGORY_VAL = #{conditions.forceVal}
            </when>
        </choose>

        <!-- 家庭子类条件 -->
        <if test="familyInfoGroupTotalVo.familySubcategoryVal != null and familyInfoGroupTotalVo.familySubcategoryVal != ''">
            AND F.FAMILY_SUBCATEGORY_VAL = #{familyInfoGroupTotalVo.familySubcategoryVal}
        </if>

        <!-- 数据状态条件 -->
        <if test="familyInfoGroupTotalVo.dataStatus != null and familyInfoGroupTotalVo.dataStatus != ''">
            AND F.DATA_STATUS = #{familyInfoGroupTotalVo.dataStatus}
            <if test="familyInfoGroupTotalVo.dataStatus == 1">
                AND F.AUDIT_STATUS = 1
                AND F.FLOW_STATUS = 2
            </if>
        </if>

        <!-- 当前状态 -->
        <if test="conditions.currentStatus != null and conditions.currentStatus != ''">
            AND F.REVIEW_STATUS = #{conditions.currentStatus}
        </if>

        <!-- 操作类别 -->
        <if test="familyInfoGroupTotalVo.operationCategory != null and familyInfoGroupTotalVo.operationCategory != ''">
            AND F.OPERATION_CATEGORY = #{familyInfoGroupTotalVo.operationCategory}
        </if>

        <!-- 流程状态 -->
        <if test="familyInfoGroupTotalVo.flowStatus != null and familyInfoGroupTotalVo.flowStatus != ''">
            AND F.FLOW_STATUS = #{familyInfoGroupTotalVo.flowStatus}
        </if>

        <include refid="hdsqsWhere"/>
        <include refid="memberWhere"/>

        <!-- 家庭编码 -->
        <if test="familyInfoGroupTotalVo.familyCode != null and familyInfoGroupTotalVo.familyCode != ''">
            AND F.FAMILY_CODE = #{familyInfoGroupTotalVo.familyCode}
        </if>

        <!-- 核对状态 -->
        <if test="familyInfoGroupTotalVo.checkState != null and familyInfoGroupTotalVo.checkState != ''">
            AND F.CHECK_STATE = #{familyInfoGroupTotalVo.checkState}
        </if>

        <!-- 是否预警 -->
        <if test="familyInfoGroupTotalVo.isWarning != null and familyInfoGroupTotalVo.isWarning != ''">
            AND F.IS_WARNING = #{familyInfoGroupTotalVo.isWarning}
        </if>

        <!-- 申请日期范围 -->
        <if test="familyInfoGroupTotalVo.startApplyDate != null and familyInfoGroupTotalVo.startApplyDate != ''">
            AND F.APPLY_DATE >= STR_TO_DATE(#{familyInfoGroupTotalVo.startApplyDate}, '%Y-%m-%d')
        </if>
        <if test="familyInfoGroupTotalVo.endApplyDate != null and familyInfoGroupTotalVo.endApplyDate != ''">
            AND F.APPLY_DATE <![CDATA[ <= ]]> STR_TO_DATE(CONCAT(#{familyInfoGroupTotalVo.endApplyDate}, ' 23:59:59'),
            '%Y-%m-%d %H:%i:%s')
        </if>

        <!-- 首次发放日期范围 -->
        <if test="familyInfoGroupTotalVo.startFirstStartDate != null and familyInfoGroupTotalVo.startFirstStartDate != ''">
            AND F.CURRENT_START_DATE >= STR_TO_DATE(CONCAT(#{familyInfoGroupTotalVo.startFirstStartDate}, '-01'),
            '%Y-%m-%d')
        </if>
        <if test="familyInfoGroupTotalVo.endFirstStartDate != null and familyInfoGroupTotalVo.endFirstStartDate != ''">
            AND F.CURRENT_START_DATE <![CDATA[ <= ]]>
            LAST_DAY(STR_TO_DATE(CONCAT(#{familyInfoGroupTotalVo.endFirstStartDate}, '-01'), '%Y-%m-%d'))
        </if>

        <!-- 贫困原因 -->
        <if test="familyInfoGroupTotalVo.povertyCauseVal != null and familyInfoGroupTotalVo.povertyCauseVal != ''">
            AND F.POVERTY_CAUSE_VAL = #{familyInfoGroupTotalVo.povertyCauseVal}
        </if>

        <!-- 单项保险 -->
        <if test="familyInfoGroupTotalVo.singleInsuranceVal != null and familyInfoGroupTotalVo.singleInsuranceVal != ''">
            AND F.SINGLE_INSURANCE_VAL = #{familyInfoGroupTotalVo.singleInsuranceVal}
        </if>

        <!-- 入户调查 -->
        <if test="familyInfoGroupTotalVo.householdStatus != null and familyInfoGroupTotalVo.householdStatus != ''">
            AND F.HOUSEHOLD_STATUS = #{familyInfoGroupTotalVo.householdStatus}
        </if>
        ) t
        GROUP BY t.areaName, t.areaCode
        <if test="familyInfoGroupTotalVo.familySubcategoryVal != null and familyInfoGroupTotalVo.familySubcategoryVal != ''">
            , t.FAMILY_SUBCATEGORY_VAL
        </if>
    </select>

    <select id="selectIndemnifyPersonsByFamilyIds"
            resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsFamilyinfoVO">
        SELECT
        M.FAMILY_ID AS ID,
        WM_CONCAT(M.PERSON_NAME) AS PERSONS_NAME
        FROM ${databaseName}.${conditions.memberTableName} M
        WHERE M.DELETE_MARK = 0
        AND M.IS_INDEMNIFY_PERSON = 1
        <if test="conditions.familyIds != null and conditions.familyIds.size > 0">
            AND M.FAMILY_ID IN
            <foreach collection="conditions.familyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY M.FAMILY_ID
    </select>

    <select id="selectHdsqsByFamilyIds" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsFamilyinfoVO">
        SELECT
        ATT.OBJECT_ID AS ID,
        CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END AS HAS_HDSQS
        FROM ${databaseName}.MSGS_ATTACHMENTINFO ATT
        WHERE ATT.DELETE_MARK = 0
        <if test="conditions.authorizationLetter != null and conditions.authorizationLetter != ''">
            AND ATT.FILE_CATEGORY = #{conditions.authorizationLetter}
        </if>
        <if test="conditions.familyIds != null and conditions.familyIds.size() > 0">
            AND ATT.OBJECT_ID IN
            <foreach collection="conditions.familyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY ATT.OBJECT_ID
    </select>

</mapper>