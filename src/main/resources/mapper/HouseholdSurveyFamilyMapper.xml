<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyFamilyMapper">

    <resultMap id="HouseholdSurveyFamilyMap"
               type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyFamily">
        <result column="id" property="id" javaType="java.lang.String"/>
        <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
        <result column="create_date" property="createDate" javaType="java.util.Date"/>
        <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
        <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
        <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
        <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
        <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
        <result column="province_code" property="provinceCode" javaType="java.lang.String"/>
        <result column="province_name" property="provinceName" javaType="java.lang.String"/>
        <result column="city_code" property="cityCode" javaType="java.lang.String"/>
        <result column="city_name" property="cityName" javaType="java.lang.String"/>
        <result column="county_code" property="countyCode" javaType="java.lang.String"/>
        <result column="county_name" property="countyName" javaType="java.lang.String"/>
        <result column="town_code" property="townCode" javaType="java.lang.String"/>
        <result column="town_name" property="townName" javaType="java.lang.String"/>
        <result column="village_code" property="villageCode" javaType="java.lang.String"/>
        <result column="village_name" property="villageName" javaType="java.lang.String"/>
        <result column="household_survey_id" property="householdSurveyId" javaType="java.lang.String"/>
        <result column="name" property="name" javaType="java.lang.String"/>
        <result column="id_card" property="idCard" javaType="java.lang.String"/>
        <result column="sex" property="sex" javaType="java.lang.Integer"/>
        <result column="nationality_val" property="nationalityVal" javaType="java.lang.String"/>
        <result column="nationality_key" property="nationalityKey" javaType="java.lang.String"/>
        <result column="family_population" property="familyPopulation" javaType="java.lang.Integer"/>
        <result column="tel" property="tel" javaType="java.lang.String"/>
        <result column="single_insurance_val" property="singleInsuranceVal" javaType="java.lang.String"/>
        <result column="single_insurance_key" property="singleInsuranceKey" javaType="java.lang.String"/>
        <result column="account_holder" property="accountHolder" javaType="java.lang.String"/>
        <result column="account_holdercard" property="accountHoldercard" javaType="java.lang.String"/>
        <result column="bank_account" property="bankAccount" javaType="java.lang.String"/>
        <result column="bank_val" property="bankVal" javaType="java.lang.String"/>
        <result column="bank_key" property="bankKey" javaType="java.lang.String"/>
        <result column="poverty_cause_val" property="povertyCauseVal" javaType="java.lang.String"/>
        <result column="poverty_cause_key" property="povertyCauseKey" javaType="java.lang.String"/>
        <result column="residence_code" property="residenceCode" javaType="java.lang.String"/>
        <result column="residence_address" property="residenceAddress" javaType="java.lang.String"/>
        <result column="domicile_code" property="domicileCode" javaType="java.lang.String"/>
        <result column="domicile_address" property="domicileAddress" javaType="java.lang.String"/>
        <result column="application_reason" property="applicationReason" javaType="java.lang.String"/>
        <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
        <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
        <result column="HOUSEHOLD_SURVEY_CONFIG_CODE" property="householdSurveyConfigCode" javaType="java.lang.String"/>
        <result column="LONGITUDE" property="longitude" javaType="java.math.BigDecimal"/>
        <result column="LATITUDE" property="latitude" javaType="java.math.BigDecimal"/>
    </resultMap>
</mapper>