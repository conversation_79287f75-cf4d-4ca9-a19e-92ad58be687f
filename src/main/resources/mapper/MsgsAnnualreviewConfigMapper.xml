<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsAnnualreviewConfigMapper">

    <resultMap id="MsgsAnnualreviewConfigMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsAnnualreviewConfig">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="WORK_NAME" property="workName" javaType="java.lang.String"/>
            <result column="AREA_CODE" property="areaCode" javaType="java.lang.String"/>
            <result column="AREA_NAME" property="areaName" javaType="java.lang.String"/>
            <result column="REVIEW_CATEGORY_VAL" property="reviewCategoryVal" javaType="java.lang.String"/>
            <result column="REVIEW_CATEGORY_KEY" property="reviewCategoryKey" javaType="java.lang.String"/>
            <result column="REVIEW_SUB_CATEGORY_VAL" property="reviewSubCategoryVal" javaType="java.lang.String"/>
            <result column="REVIEW_SUB_CATEGORY_KEY" property="reviewSubCategoryKey" javaType="java.lang.String"/>
            <result column="REVIEW_OPPORTUNITY" property="reviewOpportunity" javaType="java.lang.String"/>
            <result column="REVIEW_TIME_LIMIT" property="reviewTimeLimit" javaType="java.lang.Integer"/>
            <result column="REVIEW_START_MONTH" property="reviewStartMonth" javaType="java.lang.Integer"/>
            <result column="REVIEW_MONTH" property="reviewMonth" javaType="java.lang.String"/>
            <result column="REMARKS" property="remarks" javaType="java.lang.String"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
    </resultMap>
</mapper>