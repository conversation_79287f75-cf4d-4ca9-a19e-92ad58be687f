<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsAnnualreviewConfirmMapper">

    <resultMap id="MsgsAnnualreviewConfirmMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsAnnualreviewConfirm">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
            <result column="REVIEW_DETAIL_ID" property="reviewDetailId" javaType="java.lang.String"/>
            <result column="FAMILY_DIFFICULTY" property="familyDifficulty" javaType="java.lang.String"/>
            <result column="FAMILY_CONDITION" property="familyCondition" javaType="java.lang.String"/>
            <result column="OTHER_CONDITION" property="otherCondition" javaType="java.lang.String"/>
            <result column="SITUATION_DESCRIPTION" property="situationDescription" javaType="java.lang.String"/>
            <result column="SPOT_CHECK_CONCLUSION" property="spotCheckConclusion" javaType="java.lang.String"/>
            <result column="IS_WARNING_RESULT_TRUE" property="isWarningResultTrue" javaType="java.lang.Integer"/>
            <result column="SURVEY_SUGGEST_VAL" property="surveySuggestVal" javaType="java.lang.String"/>
            <result column="SURVEY_SUGGEST_KEY" property="surveySuggestKey" javaType="java.lang.String"/>
            <result column="SURVEY_ORG" property="surveyOrg" javaType="java.lang.String"/>
            <result column="SURVEY_DATE" property="surveyDate" javaType="java.util.Date"/>
    </resultMap>
</mapper>