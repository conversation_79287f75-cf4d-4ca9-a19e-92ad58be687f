<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsAnnualreviewBatchMapper">

    <resultMap id="MsgsAnnualreviewBatchMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsAnnualreviewBatch">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="BATCH_NO" property="batchNo" javaType="java.lang.String"/>
            <result column="WORK_NAME" property="workName" javaType="java.lang.String"/>
            <result column="AREA_CODE" property="areaCode" javaType="java.lang.String"/>
            <result column="AREA_NAME" property="areaName" javaType="java.lang.String"/>
            <result column="REVIEW_CATEGORY_VAL" property="reviewCategoryVal" javaType="java.lang.String"/>
            <result column="REVIEW_CATEGORY_KEY" property="reviewCategoryKey" javaType="java.lang.String"/>
            <result column="REVIEW_SUB_CATEGORY_VAL" property="reviewSubCategoryVal" javaType="java.lang.String"/>
            <result column="REVIEW_SUB_CATEGORY_KEY" property="reviewSubCategoryKey" javaType="java.lang.String"/>
            <result column="REVIEW_OPPORTUNITY" property="reviewOpportunity" javaType="java.lang.String"/>
            <result column="REVIEW_TIME_LIMIT" property="reviewTimeLimit" javaType="java.lang.Integer"/>
            <result column="REVIEW_START_MONTH" property="reviewStartMonth" javaType="java.lang.Integer"/>
            <result column="REVIEW_MONTH" property="reviewMonth" javaType="java.lang.String"/>
            <result column="REVIEW_TASK_STATUS" property="reviewTaskStatus" javaType="java.lang.Integer"/>
            <result column="REVIEW_RESULT_DESC" property="reviewResultDesc" javaType="java.lang.String"/>
            <result column="REVIEW_DATE" property="reviewDate" javaType="java.util.Date"/>
            <result column="REMARKS" property="remarks" javaType="java.lang.String"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
    </resultMap>
        <select id="selectMsgsFamilyinfoList"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsFamilyinfo">
                SELECT
                ID, HOUSEHOLDER_ID_CARD, PERSON_COUNT_ON_FLOW, CLASSIFY_FUND,FIRST_START_DATE, PROVINCE_CODE, PROVINCE_NAME, CITY_CODE, CITY_NAME, COUNTY_CODE, COUNTY_NAME, TOWN_CODE,
                TOWN_NAME, VILLAGE_CODE, VILLAGE_NAME, PERSON_COUNT, RELIEF_FUND, TOTAL_FUND, HOUSEHOLDER
                FROM ${databaseName}.msgs_familyinfo
                WHERE DELETE_MARK = 0  and DATA_STATUS = 1
                AND AUDIT_STATUS = 1 AND FLOW_STATUS = 2
                <if test="params.familyCategoryVal !=null and params.familyCategoryVal !=''">
                        AND FAMILY_CATEGORY_VAL = #{params.familyCategoryVal}
                </if>
                <if test="params.areaCode !=null and params.areaCode !=''">
                        and COUNTY_CODE like #{params.areaCode} || '%'
                </if>

        </select>
        <select id="selectMsgsAnnualreviewBatchList"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsAnnualreviewBatchVO">
                SELECT
                ID, BATCH_NO, WORK_NAME, AREA_CODE,AREA_NAME,  REVIEW_CATEGORY_VAL, REVIEW_CATEGORY_KEY, REVIEW_OPPORTUNITY, REVIEW_TIME_LIMIT, REVIEW_START_MONTH,
                REVIEW_TASK_STATUS,  REVIEW_DATE, CREATE_DATE, b.reviewHolders, b.reviewCount, b.reviewNoCount
                FROM ${databaseName}.MSGS_ANNUALREVIEW_BATCH a INNER  JOIN (SELECT TASK_ID,count(id) AS reviewHolders,
                sum(CASE WHEN REVIEW_STATUS_VAL=1 THEN 1 ELSE 0 END ) AS reviewCount, sum(CASE WHEN REVIEW_STATUS_VAL=0 THEN 1 ELSE 0 END ) AS reviewNoCount
                FROM ${databaseName}.MSGS_ANNUALREVIEW_DETAIL GROUP BY TASK_ID) b ON a.ID=b.TASK_ID
                WHERE DELETE_MARK = 0
                <if test="params.reviewCategoryVal !=null and params.reviewCategoryVal !=''">
                        AND REVIEW_CATEGORY_VAL = #{params.reviewCategoryVal}
                </if>
                <if test="params.matchCode !=null and params.matchCode !=''">
                        AND AREA_CODE like #{params.matchCode} || '%'
                </if>
                <if test="params.batchNo !=null and params.batchNo !=''">
                        AND BATCH_NO = #{params.batchNo}
                </if>

        </select>

        <select id="getOne"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsAnnualreviewBatchInfoVO">
                SELECT
                ID, BATCH_NO, WORK_NAME, AREA_CODE,AREA_NAME, REVIEW_CATEGORY_VAL, REVIEW_CATEGORY_KEY,REVIEW_TASK_STATUS, b.reviewHolders
                ,b.PERSON_COUNT_ON_FLOW,b.TOTAL_FUND,b.CHANGE_FAMILY_VALUE,b.CHANGE_MONEY_VALUE,b.CHANGE_PERSON_VALUE,b.reviewCount
                FROM ${databaseName}.MSGS_ANNUALREVIEW_BATCH a INNER  JOIN (SELECT TASK_ID,count(id) AS reviewHolders,sum(CASE WHEN REVIEW_STATUS_VAL=1 THEN 1 ELSE 0 END ) AS reviewCount,
                sum(PERSON_COUNT_ON_FLOW) AS PERSON_COUNT_ON_FLOW, sum(TOTAL_FUND) AS TOTAL_FUND
                ,sum(ISNULL (CASE when CHANGE_PERSON_TYPE=1 THEN CHANGE_PERSON_VALUE ELSE -CHANGE_PERSON_VALUE END,0)) AS CHANGE_PERSON_VALUE
                ,sum(ISNULL (CASE when CHANGE_FAMILY_TYPE=1 THEN CHANGE_FAMILY_VALUE ELSE -CHANGE_FAMILY_VALUE END,0)) AS CHANGE_FAMILY_VALUE
                ,sum(ISNULL (CASE when CHANGE_MONEY_TYPE=1 THEN CHANGE_MONEY_VALUE ELSE -CHANGE_MONEY_VALUE END,0)) AS CHANGE_MONEY_VALUE
                FROM ${databaseName}.MSGS_ANNUALREVIEW_DETAIL  WHERE TASK_ID=#{id} GROUP BY TASK_ID) b ON a.ID=b.TASK_ID
                WHERE DELETE_MARK = 0 AND ID = #{id}
        </select>

        <select id="getStatisticsCount"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.StatisticsCountVO">
                SELECT count(1) as total,isnull(sum(PERSON_COUNT_ON_FLOW),0) as personCount
                FROM ${databaseName}.msgs_familyinfo
                WHERE DELETE_MARK = 0  and DATA_STATUS = 1
                AND AUDIT_STATUS = 1 AND FLOW_STATUS = 2
                <if test="params.familyCategoryVal !=null and params.familyCategoryVal !=''">
                        AND FAMILY_CATEGORY_VAL = #{params.familyCategoryVal}
                </if>
                <if test="params.familySubcategoryVal !=null and params.familySubcategoryVal !=''">
                        AND FAMILY_SUBCATEGORY_VAL = #{params.familySubcategoryVal}
                </if>
                <if test="params.level ==1">
                        and PROVINCE_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==2">
                        and CITY_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==3">
                        and COUNTY_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==4">
                        and TOWN_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==5">
                        and VILLAGE_CODE = #{params.areaCode}
                </if>
                <if test="params.householderIdCard !=null and params.householderIdCard !=''">
                        AND HOUSEHOLDER_ID_CARD = #{params.householderIdCard}
                </if>
                <if test="null != params.startDate and '' != params.startDate">
                        AND DATE_FORMAT(FIRST_START_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{params.startDate}
                </if>
                <if test="null != params.endDate and '' != params.endDate">
                        AND DATE_FORMAT(FIRST_START_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{params.endDate}
                </if>
        </select>

        <select id="selectReviewFamilyinfoList"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsFamilyinfo">
                SELECT
                ID, HOUSEHOLDER_ID_CARD, PERSON_COUNT_ON_FLOW, CLASSIFY_FUND,FIRST_START_DATE, PROVINCE_CODE, PROVINCE_NAME, CITY_CODE, CITY_NAME, COUNTY_CODE, COUNTY_NAME, TOWN_CODE,
                TOWN_NAME, VILLAGE_CODE, VILLAGE_NAME, PERSON_COUNT, RELIEF_FUND, TOTAL_FUND, HOUSEHOLDER
                FROM ${databaseName}.msgs_familyinfo
                WHERE DELETE_MARK = 0  and DATA_STATUS = 1
                AND AUDIT_STATUS = 1 AND FLOW_STATUS = 2
                <if test="params.familyCategoryVal !=null and params.familyCategoryVal !=''">
                        AND FAMILY_CATEGORY_VAL = #{params.familyCategoryVal}
                </if>
                <if test="params.familySubcategoryVal !=null and params.familySubcategoryVal !=''">
                        AND FAMILY_SUBCATEGORY_VAL = #{params.familySubcategoryVal}
                </if>
                <if test="params.level ==1">
                        and PROVINCE_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==2">
                        and CITY_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==3">
                        and COUNTY_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==4">
                        and TOWN_CODE = #{params.areaCode}
                </if>
                <if test="params.level ==5">
                        and VILLAGE_CODE = #{params.areaCode}
                </if>
                <if test="params.householderIdCard !=null and params.householderIdCard !=''">
                        AND HOUSEHOLDER_ID_CARD = #{params.householderIdCard}
                </if>
                <if test="null != params.startDate and '' != params.startDate">
                        AND DATE_FORMAT(a.FIRST_START_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{params.startDate}
                </if>
                <if test="null != params.endDate and '' != params.endDate">
                        AND DATE_FORMAT(a.FIRST_START_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{params.endDate}
                </if>

        </select>
</mapper>