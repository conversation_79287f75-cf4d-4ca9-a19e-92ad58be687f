<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsCheckDetailMapper">

    <select id="selectListByBatchId"
            resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.BatchCheckDetailVO">
        SELECT t1.PERSON_COUNT,
               t1.PEOPLES,
               t1.IS_WARNING,
               t1.STATUS,
               t1.IDCARDS,
               t1.CHECK_MESSAGE,
               t1.FILE_PATH,
               t1.FILE_NAME,
               t2.HOUSEHOLDER,
               t2.HOUSEHOLDER_ID_CARD,
               t2.CITY_NAME || '-' || t2.COUNTY_NAME || '-' || t2.TOWN_NAME || '-' || t2.VILLAGE_NAME AREANAME,
               t2.RESIDENCE_ADDRESS,
               t2.FAMILY_CATEGORY_KEY
        FROM ${databaseName}.MSGS_CHECKDETAIL t1
                 INNER JOIN ${databaseName}.MSGS_FAMILYINFO t2 ON t1.FAMILY_ID = t2.ID
        WHERE t1.DELETE_MARK = 0
          AND t2.DELETE_MARK = 0
          AND t1.BATCH_ID = #{batchId}
    </select>

    <select id="selectLatestList" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsCheckDetail">
        SELECT *
        FROM (
        SELECT ID,FAMILY_ID,FILE_PATH,FILE_NAME,REPORTING_DATE,SYNCHRONOUS_DATE,IS_WARNING,
        ROW_NUMBER() OVER (PARTITION BY FAMILY_ID ORDER BY REPORTING_DATE DESC) AS rn
        FROM ${databaseName}.MSGS_CHECKDETAIL
        WHERE FILE_PATH IS NOT NULL
        AND FILE_PATH != ''
        AND DELETE_MARK = 0
        <if test="params.familyIds != null and params.familyIds.size() > 0">
            AND FAMILY_ID IN
            <foreach collection="params.familyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ) t
        WHERE t.rn = 1
    </select>

    <update id="batchUpdateDetailStatus">
        <foreach collection="list" item="item" separator=";">
            UPDATE ${databaseName}.MSGS_CHECKDETAIL
            SET
            FILE_PATH = #{item.filePath},
            FILE_NAME = #{item.fileName},
            STATUS = #{item.status},
            CHECK_MESSAGE = #{item.checkMessage},
            MODIFY_DATE = #{item.modifyDate},
            MODIFY_USER_ID = #{item.modifyUserId},
            MODIFY_USER_NAME = #{item.modifyUserName}
            -- 同步成功
            <if test="item.status == 3">
                , SYNCHRONOUS_DATE = #{item.modifyDate}
            </if>
            -- 核对通过
            <if test="item.status == 5">
                , REPORTING_DATE = #{item.modifyDate}
            </if>
            WHERE ID = #{item.id}
        </foreach>
    </update>
    
</mapper>