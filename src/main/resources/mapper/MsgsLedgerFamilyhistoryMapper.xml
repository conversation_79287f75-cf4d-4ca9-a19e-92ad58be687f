<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsLedgerFamilyhistoryMapper">
    <select id="selectFamilyhistoryList" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsLedgerFamilyVO">
        SELECT    f.ID ,f."YEAR" ,f."MONTH",f.TOTAL_FUND,f.HOUSEHOLDER ,f.HOUSEHOLDER_ID_CARD ,f.FINAL_MONEY,f.FAMILY_CATEGORY_VAL,f.COUNTY_CODE,
        (SELECT  WM_CONCAT(PERSON_NAME) FROM  ${databaseName}.MSGS_FAMILYMEMBERINFO WHERE  DELETE_MARK =0 AND FAMILY_ID =f.FAMILY_ID AND IS_INDEMNIFY_PERSON=1 )  PERSON_NAME_GUARANTEE,
        (SELECT  WM_CONCAT(PERSON_ID_CARD) FROM  ${databaseName}.MSGS_FAMILYMEMBERINFO WHERE  DELETE_MARK =0 AND FAMILY_ID =f.FAMILY_ID AND IS_INDEMNIFY_PERSON=1 )   PERSON_CARD_GUARANTEE
        FROM ${databaseName}.MSGS_LEDGER_FAMILY f  where f.FAMILY_CODE=#{params.familyCode}
        <if test="params.areaLevel ==3">
            and F.COUNTY_CODE = #{params.areaCode}
        </if>
        <if test="params.startMonth !=null and params.startMonth !=null" >
            and F.LEDGER_DATE>= #{params.startMonth }
        </if>

        <if test="params.endMonth !=null and params.endMonth !=null" >
            <![CDATA[ and F.LEDGER_DATE<= #{params.endMonth }]]>
        </if>
            union all
        SELECT   f.ID ,f."YEAR" ,f."MONTH",f.TOTAL_FUND,f.HOUSEHOLDER ,f.HOUSEHOLDER_ID_CARD ,f.FINAL_MONEY,f.FAMILY_CATEGORY_VAL,f.COUNTY_CODE,
        (SELECT  WM_CONCAT(PERSON_NAME) FROM  ${databaseName}.MSGS_FAMILYMEMBERINFO WHERE  DELETE_MARK =0 AND FAMILY_ID =f.FAMILY_ID AND IS_INDEMNIFY_PERSON=1 )  PERSON_NAME_GUARANTEE,
        (SELECT  WM_CONCAT(PERSON_ID_CARD) FROM  ${databaseName}.MSGS_FAMILYMEMBERINFO WHERE  DELETE_MARK =0 AND FAMILY_ID =f.FAMILY_ID AND IS_INDEMNIFY_PERSON=1 )   PERSON_CARD_GUARANTEE

        FROM ${databaseName}.MSGS_LEDGER_FAMILYHISTORY f   where   f.FAMILY_CODE=#{params.familyCode}
        <if test="params.areaLevel ==3">
            and F.COUNTY_CODE = #{params.areaCode}
        </if>
        <if test="params.startMonth !=null and params.startMonth !=''" >
            and F.LEDGER_DATE>= #{params.startMonth }
        </if>
        <if test="params.endMonth !=null and params.endMonth !=''" >
            <![CDATA[ and F.LEDGER_DATE<= #{params.endMonth }]]>
        </if>
        order by "YEAR" desc,"MONTH" desc
    </select>

    <select id="selectLedgerTotalInfo" resultType="Integer">
        SELECT  count(1)
        FROM ${databaseName}.MSGS_LEDGER_TOTAL f WHERE DELETE_MARK =0 AND STATUS =1
        <if test="params.month !=null and params.month !=''" >
            and F.MONTH= #{params.month }
        </if>
        <if test="params.year !=null and params.year !=''" >
            and F.YEAR= #{params.year }
        </if>
        <if test="params.areaCode !=null and params.areaCode !=''">
            and F.COUNTY_CODE = #{params.areaCode}
        </if>
        <if test="params.familyCategoryVal !=null and params.familyCategoryVal !=''" >
            and F.FAMILY_CATEGORY_VAL= #{params.familyCategoryVal }
        </if>

    </select>
</mapper>