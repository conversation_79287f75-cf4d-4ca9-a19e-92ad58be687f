<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyAttachmentInfoMapper">

    <resultMap id="HouseholdSurveyAttachmentInfoMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyAttachmentInfo">
            <result column="id" property="id" javaType="java.lang.String"/>
            <result column="object_type" property="objectType" javaType="java.lang.Integer"/>
            <result column="object_id" property="objectId" javaType="java.lang.String"/>
            <result column="file_category" property="fileCategory" javaType="java.lang.String"/>
            <result column="file_name" property="fileName" javaType="java.lang.String"/>
            <result column="file_path" property="filePath" javaType="java.lang.String"/>
            <result column="file_size" property="fileSize" javaType="java.lang.String"/>
            <result column="file_extensions" property="fileExtensions" javaType="java.lang.String"/>
            <result column="file_type" property="fileType" javaType="java.lang.String"/>
            <result column="download_count" property="downloadCount" javaType="java.lang.Integer"/>
            <result column="is_top" property="isTop" javaType="java.lang.Integer"/>
            <result column="sort_code" property="sortCode" javaType="java.lang.Integer"/>
            <result column="enabled_mark" property="enabledMark" javaType="java.lang.Integer"/>
            <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="create_date" property="createDate" javaType="java.util.Date"/>
            <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
            <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
            <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
            <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
            <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
            <result column="member_id" property="memberId" javaType="java.lang.String"/>
            <result column="file_category_name" property="fileCategoryName" javaType="java.lang.String"/>
            <result column="RELATED_TABLE_MEMBER_ID" property="relatedTableMemberId" javaType="java.lang.String"/>
            <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
            <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
    </resultMap>
</mapper>