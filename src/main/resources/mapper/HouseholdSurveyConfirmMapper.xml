<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyConfirmMapper">

    <resultMap id="HouseholdSurveyConfirmMap"
               type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyConfirm">
        <result column="id" property="id" javaType="java.lang.String"/>
        <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
        <result column="create_date" property="createDate" javaType="java.util.Date"/>
        <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
        <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
        <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
        <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
        <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
        <result column="household_survey_id" property="householdSurveyId" javaType="java.lang.String"/>
        <result column="family_difficulty" property="familyDifficulty" javaType="java.lang.String"/>
        <result column="family_condition" property="familyCondition" javaType="java.lang.String"/>
        <result column="other_condition" property="otherCondition" javaType="java.lang.String"/>
        <result column="situation_description" property="situationDescription" javaType="java.lang.String"/>
        <result column="spot_check_conclusion" property="spotCheckConclusion" javaType="java.lang.String"/>
        <result column="is_warning_result_true" property="isWarningResultTrue" javaType="java.lang.Integer"/>
        <result column="survey_suggest_val" property="surveySuggestVal" javaType="java.lang.String"/>
        <result column="survey_suggest_key" property="surveySuggestKey" javaType="java.lang.String"/>
        <result column="survey_org" property="surveyOrg" javaType="java.lang.String"/>
        <result column="survey_date" property="surveyDate" javaType="java.util.Date"/>
        <result column="scene_photos" property="scenePhotos" javaType="java.lang.String"/>
        <result column="survey_sign" property="surveySign" javaType="java.lang.String"/>
        <result column="object_sign" property="objectSign" javaType="java.lang.String"/>
        <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
        <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
        <result column="HOUSEHOLD_SURVEY_CONFIG_CODE" property="householdSurveyConfigCode" javaType="java.lang.String"/>
        <result column="LOCATION_ADDRESS" property="locationAddress" javaType="java.lang.String"/>
        <result column="LONGITUDE" property="longitude" javaType="java.math.BigDecimal"/>
        <result column="LATITUDE" property="latitude" javaType="java.math.BigDecimal"/>
    </resultMap>
</mapper>