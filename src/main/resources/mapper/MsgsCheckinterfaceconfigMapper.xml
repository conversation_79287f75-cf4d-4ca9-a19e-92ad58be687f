<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsCheckinterfaceconfigMapper">

    <resultMap id="MsgsCheckinterfaceconfigMap" type="net.jqsoft.zhmz.cloudplatform.msgs.entity.MsgsCheckinterfaceconfig">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="AREA_CODE" property="areaCode" javaType="java.lang.String"/>
            <result column="AREA_NAME" property="areaName" javaType="java.lang.String"/>
            <result column="CHECK_INTERFACE_A_P_I" property="checkInterfaceAPI" javaType="java.lang.String"/>
            <result column="REMARK" property="remark" javaType="java.lang.String"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
    </resultMap>
</mapper>