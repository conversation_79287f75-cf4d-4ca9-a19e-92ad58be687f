<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyFamilyIncomeMapper">

    <resultMap id="HouseholdSurveyFamilyIncomeMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyFamilyIncome">
            <result property="id" column="ID"/>
            <result property="deleteMark" column="DELETE_MARK"/>
            <result property="createDate" column="CREATE_DATE"/>
            <result property="createUserId" column="CREATE_USER_ID"/>
            <result property="createUserName" column="CREATE_USER_NAME"/>
            <result property="modifyDate" column="MODIFY_DATE"/>
            <result property="modifyUserId" column="MODIFY_USER_ID"/>
            <result property="modifyUserName" column="MODIFY_USER_NAME"/>
            <result property="householdSurveyId" column="HOUSEHOLD_SURVEY_ID"/>
            <result property="typeVal" column="TYPE_VAL"/>
            <result property="typeKey" column="TYPE_KEY"/>
            <result property="amount" column="AMOUNT"/>
            <result property="includedAmount" column="INCLUDED_AMOUNT"/>
            <result property="excludedAmount" column="EXCLUDED_AMOUNT"/>
            <result property="description" column="DESCRIPTION"/>
            <result property="relatedTableId" column="RELATED_TABLE_ID"/>
            <result property="relatedTableName" column="RELATED_TABLE_NAME"/>
    </resultMap>
</mapper>