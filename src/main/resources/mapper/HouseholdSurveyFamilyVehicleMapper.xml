<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyFamilyVehicleMapper">

    <resultMap id="HouseholdSurveyFamilyVehicleMap"
               type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyFamilyVehicle">
        <result column="id" property="id" javaType="java.lang.String"/>
        <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
        <result column="create_date" property="createDate" javaType="java.util.Date"/>
        <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
        <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
        <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
        <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
        <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
        <result column="household_survey_id" property="householdSurveyId" javaType="java.lang.String"/>
        <result column="name" property="name" javaType="java.lang.String"/>
        <result column="model" property="model" javaType="java.lang.String"/>
        <result column="vehicle_number" property="vehicleNumber" javaType="java.lang.String"/>
        <result column="exhaust_capacity" property="exhaustCapacity" javaType="java.lang.String"/>
        <result column="purchase_time" property="purchaseTime" javaType="java.util.Date"/>
        <result column="purchase_amount" property="purchaseAmount" javaType="java.math.BigDecimal"/>
        <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
        <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
    </resultMap>
</mapper>