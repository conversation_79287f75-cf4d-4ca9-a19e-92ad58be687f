<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsAnnualreviewDetailMapper">

    <resultMap id="MsgsAnnualreviewDetailMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsAnnualreviewDetail">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="TASK_ID" property="taskId" javaType="java.lang.String"/>
            <result column="FAMILY_ID" property="familyId" javaType="java.lang.String"/>
            <result column="REVIEW_STATUS_VAL" property="reviewStatusVal" javaType="java.lang.Integer"/>
            <result column="REVIEW_STATUSKEY" property="reviewStatuskey" javaType="java.lang.String"/>
            <result column="REVIEW_TYPE" property="reviewType" javaType="java.lang.Integer"/>
            <result column="IS_END" property="isEnd" javaType="java.lang.Integer"/>
            <result column="CHANGE_PERSON_TYPE" property="changePersonType" javaType="java.lang.Integer"/>
            <result column="CHANGE_PERSON_VALUE" property="changePersonValue" javaType="java.lang.Integer"/>
            <result column="CHANGE_FAMILY_TYPE" property="changeFamilyType" javaType="java.lang.Integer"/>
            <result column="CHANGE_FAMILY_VALUE" property="changeFamilyValue" javaType="java.lang.Integer"/>
            <result column="CHANGE_MONEY_TYPE" property="changeMoneyType" javaType="java.lang.Integer"/>
            <result column="CHANGE_MONEY_VALUE" property="changeMoneyValue" javaType="java.math.BigDecimal"/>
            <result column="REVIEW_RESULT_DESC" property="reviewResultDesc" javaType="java.lang.String"/>
            <result column="REVIEW_DATE" property="reviewDate" javaType="java.util.Date"/>
            <result column="CONSISTENT" property="consistent" javaType="java.lang.Integer"/>
            <result column="TRANSFER_TYPE" property="transferType" javaType="java.lang.Integer"/>
            <result column="INVESTIGATION" property="investigation" javaType="java.lang.String"/>
            <result column="SURVEY_CONCLUSIONS" property="surveyConclusions" javaType="java.lang.String"/>
            <result column="INVESTIGATOR" property="investigator" javaType="java.lang.String"/>
            <result column="INVESTIGATION_DATE" property="investigationDate" javaType="java.util.Date"/>
            <result column="HOUSEHOLDER_ID_CARD" property="householderIdCard" javaType="java.lang.String"/>
            <result column="PERSON_COUNT_ON_FLOW" property="personCountOnFlow" javaType="java.lang.Integer"/>
            <result column="CLASSIFY_FUND" property="classifyFund" javaType="java.math.BigDecimal"/>
            <result column="START_DATE" property="startDate" javaType="java.util.Date"/>
            <result column="REMARKS" property="remarks" javaType="java.lang.String"/>
            <result column="PROVINCE_CODE" property="provinceCode" javaType="java.lang.String"/>
            <result column="PROVINCE_NAME" property="provinceName" javaType="java.lang.String"/>
            <result column="CITY_CODE" property="cityCode" javaType="java.lang.String"/>
            <result column="CITY_NAME" property="cityName" javaType="java.lang.String"/>
            <result column="COUNTY_CODE" property="countyCode" javaType="java.lang.String"/>
            <result column="COUNTY_NAME" property="countyName" javaType="java.lang.String"/>
            <result column="TOWN_CODE" property="townCode" javaType="java.lang.String"/>
            <result column="TOWN_NAME" property="townName" javaType="java.lang.String"/>
            <result column="VILLAGE_CODE" property="villageCode" javaType="java.lang.String"/>
            <result column="VILLAGE_NAME" property="villageName" javaType="java.lang.String"/>
            <result column="PERSON_COUNT" property="personCount" javaType="java.lang.Integer"/>
            <result column="RELIEF_FUND" property="reliefFund" javaType="java.math.BigDecimal"/>
            <result column="TOTAL_FUND" property="totalFund" javaType="java.math.BigDecimal"/>
            <result column="HOUSEHOLDER" property="householder" javaType="java.lang.String"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
            <result column="DATA_STATUS" property="dataStatus" javaType="java.lang.Integer"/>
            <result column="TEL" property="tel" javaType="java.lang.String"/>
    </resultMap>

        <select id="selectMsgsAnnualreviewDetailList"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsAnnualreviewDetailVO">
                SELECT a.ID, TASK_ID, FAMILY_ID, REVIEW_STATUS_VAL, REVIEW_STATUSKEY, REVIEW_TYPE, IS_END, HOUSEHOLDER_ID_CARD, PERSON_COUNT_ON_FLOW,  START_DATE, CITY_CODE, CITY_NAME, COUNTY_CODE, COUNTY_NAME,
                PERSON_COUNT, TOTAL_FUND, HOUSEHOLDER,a.TEL,a.DATA_STATUS, a.DELETE_MARK, a.CREATE_DATE,  a.MODIFY_DATE,b.BATCH_NO ,b.WORK_NAME,b.REVIEW_CATEGORY_KEY,
                b.REVIEW_SUB_CATEGORY_KEY ,a.REVIEW_DATE ,a.END_DATE ,a.SURVEY_DATE ,a.HOUSEHOLD_STATUS ,a.HOUSEHOLD_DATE
                FROM ${databaseName}.MSGS_ANNUALREVIEW_DETAIL a INNER JOIN ${databaseName}.MSGS_ANNUALREVIEW_BATCH b ON a.TASK_ID =b.ID
                WHERE a.DELETE_MARK = 0
                <if test="params.reviewCategoryVal !=null and params.reviewCategoryVal !=''">
                        AND b.REVIEW_CATEGORY_VAL = #{params.reviewCategoryVal}
                </if>
                <if test="params.matchCode !=null and params.matchCode !=''">
                        AND b.AREA_CODE like #{params.matchCode} || '%'
                </if>
                <if test="params.batchNo !=null and params.batchNo !=''">
                        AND b.BATCH_NO = #{params.batchNo}
                </if>
                <if test="params.householderIdCard !=null and params.householderIdCard !=''">
                        AND a.HOUSEHOLDER_ID_CARD = #{params.householderIdCard}
                </if>
                <if test="params.householder !=null and params.householder !=''">
                        AND a.HOUSEHOLDER like CONCAT('%',#{params.householder},'%')
                </if>
                <if test="params.reviewStatusVal !=null ">
                        AND a.REVIEW_STATUS_VAL = #{params.reviewStatusVal}
                </if>
                <if test="params.dataStatus !=null">
                        AND a.DATA_STATUS = #{params.dataStatus}
                </if>
                <if test="params.taskId !=null and params.taskId !=''">
                        AND a.TASK_ID = #{params.taskId}
                </if>

        </select>

        <select id="getAnnualreviewDetailList"
                resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.MsgsAnnualreviewDetailVO">
                SELECT a.ID ,a.CREATE_DATE ,b.REVIEW_TIME_LIMIT FROM ${databaseName}.MSGS_ANNUALREVIEW_DETAIL a INNER JOIN ${databaseName}.MSGS_ANNUALREVIEW_BATCH b ON a.TASK_ID =b.ID
                WHERE a.DELETE_MARK =0 AND a.REVIEW_STATUS_VAL =0


        </select>
</mapper>