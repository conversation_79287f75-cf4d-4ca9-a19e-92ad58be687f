<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsHousepropertyinfoMapper">

    <resultMap id="MsgsHousepropertyinfoMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsHousepropertyinfo">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="FAMILY_ID" property="familyId" javaType="java.lang.String"/>
            <result column="HOUSE_NATURE" property="houseNature" javaType="java.lang.String"/>
            <result column="HOUSE_STRUCTURE" property="houseStructure" javaType="java.lang.String"/>
            <result column="HOUSE_ID_NUMBER" property="houseIdNumber" javaType="java.lang.String"/>
            <result column="HOUSE_AREAS" property="houseAreas" javaType="java.math.BigDecimal"/>
            <result column="HOUSE_ACTUAL_AREA" property="houseActualArea" javaType="java.math.BigDecimal"/>
            <result column="HOUSE_ADDRESS" property="houseAddress" javaType="java.lang.String"/>
            <result column="HOUSE_SOURCE" property="houseSource" javaType="java.lang.String"/>
            <result column="BUY_HOUSE_TIME" property="buyHouseTime" javaType="java.util.Date"/>
            <result column="HOUSE_TYPE" property="houseType" javaType="java.lang.Integer"/>
            <result column="PURCHASE_AMOUNT" property="purchaseAmount" javaType="java.math.BigDecimal"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
    </resultMap>
</mapper>