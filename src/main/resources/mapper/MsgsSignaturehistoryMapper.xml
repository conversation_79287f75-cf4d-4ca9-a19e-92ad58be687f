<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsSignaturehistoryMapper">

    <resultMap id="MsgsSignaturehistoryMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsSignaturehistory">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="FAMILY_ID" property="familyId" javaType="java.lang.String"/>
            <result column="FAMILY_OPERATION_ID" property="familyOperationId" javaType="java.lang.String"/>
            <result column="SIGN_AREA_CODE" property="signAreaCode" javaType="java.lang.String"/>
            <result column="SIGN_AREA_NAME" property="signAreaName" javaType="java.lang.String"/>
            <result column="SIGN_DATA" property="signData" javaType="java.lang.String"/>
            <result column="CLOUMN_JSON" property="cloumnJson" javaType="java.lang.String"/>
            <result column="REMARK" property="remark" javaType="java.lang.String"/>
            <result column="LAYER" property="layer" javaType="java.lang.Integer"/>
            <result column="SIGNATURE_TYPE" property="signatureType" javaType="java.lang.Integer"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
            <result column="DOCUMENT_TYPE" property="documentType" javaType="java.lang.Integer"/>
    </resultMap>
        <select id="getSignatureList" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.SignatureVO">
        SELECT t.* FROM (
                SELECT
                t2.CREATE_DATE ,
                t2.SIGN_DATA,
                t1.ID as FAMILY_ID,
                t1.FAMILY_CODE,
                t1.OPERATION_CATEGORY ,
                t1.HOUSEHOLDER ,
                t1.HOUSEHOLDER_ID_CARD ,
                t1.PERSON_COUNT_ON_FLOW ,
                t1.TOTAL_FUND ,
                t1.APPLY_DATE
                FROM
                ${databaseName}.MSGS_FAMILYINFO t1
                LEFT JOIN ${databaseName}.MSGS_SIGNATUREHISTORY t2 ON
                t1.ID = t2.FAMILY_ID
                WHERE
                t1.DELETE_MARK = 0
                AND t2.DELETE_MARK = 0
                AND t1.FAMILY_CODE = #{params.familyCode}
                <if test="null != params.startDate and '' != params.startDate">
                        AND DATE_FORMAT(t1.APPLY_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{params.startDate}
                    </if>
                    <if test="null != params.endDate and '' != params.endDate">
                        AND DATE_FORMAT(t1.APPLY_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{params.endDate}
                    </if>
                UNION ALL
                SELECT
                t2.CREATE_DATE ,
                t2.SIGN_DATA,
                t1.ID as FAMILY_ID,
                t1.FAMILY_CODE,
                t1.OPERATION_CATEGORY ,
                t1.HOUSEHOLDER ,
                t1.HOUSEHOLDER_ID_CARD ,
                t1.PERSON_COUNT_ON_FLOW ,
                t1.TOTAL_FUND ,
                t1.APPLY_DATE
                FROM
                ${databaseName}.MSGS_FAMILYINFOHISTORY t1
                LEFT JOIN ${databaseName}.MSGS_SIGNATUREHISTORY t2 ON
                t1.ID = t2.FAMILY_ID
                WHERE
                t1.DELETE_MARK = 0
                AND t2.DELETE_MARK = 0
                AND t1.FAMILY_CODE = #{params.familyCode}
                <if test="null != params.startDate and '' != params.startDate">
                        AND DATE_FORMAT(t1.APPLY_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{params.startDate}
                    </if>
                    <if test="null != params.endDate and '' != params.endDate">
                            AND DATE_FORMAT(t1.APPLY_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{params.endDate}
                    </if>
            ) t
        </select>


        <select id="getTassSignatureList" resultType="net.jqsoft.zhmz.cloudplatform.msgs.model.vo.SignatureVO">
                SELECT t.* FROM (
                SELECT
                t2.CREATE_DATE ,
                t2.SIGN_DATA,
                t1.ID as FAMILY_ID,
                t1.BUSINESS_NO as FAMILY_CODE,
                0 as OPERATION_CATEGORY ,
                t1.HOUSEHOLDER ,
                t1.HOUSEHOLDER_ID_CARD ,
                t1.PERSON_COUNT as PERSON_COUNT_ON_FLOW ,
                t1.HELP_MONEY as TOTAL_FUND ,
                t1.APPLY_DATE
                FROM
                ${databaseName}.TASS_FAMILY_INFO t1
                LEFT JOIN ${databaseName}.MSGS_SIGNATUREHISTORY t2 ON
                t1.ID = t2.FAMILY_ID
                WHERE
                t1.DELETE_MARK = 0
                AND t2.DELETE_MARK = 0
                AND t1.ID = #{params.familyId}
                <if test="null != params.startDate and '' != params.startDate">
                        AND DATE_FORMAT(t1.APPLY_DATE, '%Y-%m-%d') <![CDATA[ >= ]]> #{params.startDate}
                </if>
                <if test="null != params.endDate and '' != params.endDate">
                        AND DATE_FORMAT(t1.APPLY_DATE, '%Y-%m-%d') <![CDATA[ <= ]]> #{params.endDate}
                </if>
                ) t
        </select>

</mapper>