<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.MsgsFamilyinfohistoryMapper">

    <resultMap id="MsgsFamilyinfohistoryMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsFamilyinfohistory">
            <result column="ID" property="id" javaType="java.lang.String"/>
            <result column="FAMILY_CODE" property="familyCode" javaType="java.lang.String"/>
            <result column="PROVINCE_CODE" property="provinceCode" javaType="java.lang.String"/>
            <result column="PROVINCE_NAME" property="provinceName" javaType="java.lang.String"/>
            <result column="CITY_CODE" property="cityCode" javaType="java.lang.String"/>
            <result column="CITY_NAME" property="cityName" javaType="java.lang.String"/>
            <result column="COUNTY_CODE" property="countyCode" javaType="java.lang.String"/>
            <result column="COUNTY_NAME" property="countyName" javaType="java.lang.String"/>
            <result column="TOWN_CODE" property="townCode" javaType="java.lang.String"/>
            <result column="TOWN_NAME" property="townName" javaType="java.lang.String"/>
            <result column="VILLAGE_CODE" property="villageCode" javaType="java.lang.String"/>
            <result column="VILLAGE_NAME" property="villageName" javaType="java.lang.String"/>
            <result column="TRUSTOR_TYPE" property="trustorType" javaType="java.lang.Integer"/>
            <result column="TRUSTOR_NAME" property="trustorName" javaType="java.lang.String"/>
            <result column="TRUSTOR_ID_CARD" property="trustorIdCard" javaType="java.lang.String"/>
            <result column="PHONE_NUMBER" property="phoneNumber" javaType="java.lang.String"/>
            <result column="FAMILY_RELATION_VAL" property="familyRelationVal" javaType="java.lang.String"/>
            <result column="FAMILY_RELATION_KEY" property="familyRelationKey" javaType="java.lang.String"/>
            <result column="HOUSEHOLDER" property="householder" javaType="java.lang.String"/>
            <result column="HOUSEHOLDER_ID_CARD" property="householderIdCard" javaType="java.lang.String"/>
            <result column="HOUSEHOLDER_PHOTO_PATH" property="householderPhotoPath" javaType="java.lang.String"/>
            <result column="TEL" property="tel" javaType="java.lang.String"/>
            <result column="ID_NUMBER" property="idNumber" javaType="java.lang.String"/>
            <result column="FAMILY_CATEGORY_VAL" property="familyCategoryVal" javaType="java.lang.String"/>
            <result column="FAMILY_CATEGORY_KEY" property="familyCategoryKey" javaType="java.lang.String"/>
            <result column="FAMILY_SUBCATEGORY_VAL" property="familySubcategoryVal" javaType="java.lang.String"/>
            <result column="FAMILY_SUBCATEGORY_KEY" property="familySubcategoryKey" javaType="java.lang.String"/>
            <result column="POVERTY_CAUSE_VAL" property="povertyCauseVal" javaType="java.lang.String"/>
            <result column="POVERTY_CAUSE_KEY" property="povertyCauseKey" javaType="java.lang.String"/>
            <result column="BANK_VAL" property="bankVal" javaType="java.lang.String"/>
            <result column="BANK_KEY" property="bankKey" javaType="java.lang.String"/>
            <result column="BANK_ACCOUNT" property="bankAccount" javaType="java.lang.String"/>
            <result column="ACCOUNT_HOLDER" property="accountHolder" javaType="java.lang.String"/>
            <result column="ACCOUNT_HOLDER_NUMBER" property="accountHolderNumber" javaType="java.lang.String"/>
            <result column="GRANT_OBJECT_TYPE" property="grantObjectType" javaType="java.lang.Integer"/>
            <result column="PERSON_COUNT" property="personCount" javaType="java.lang.Integer"/>
            <result column="PERSON_COUNT_ON_FLOW" property="personCountOnFlow" javaType="java.lang.Integer"/>
            <result column="MONTH_INCOME" property="monthIncome" javaType="java.math.BigDecimal"/>
            <result column="PERSON_AVG_INCOME" property="personAvgIncome" javaType="java.math.BigDecimal"/>
            <result column="MAIN_EXPENSES" property="mainExpenses" javaType="java.math.BigDecimal"/>
            <result column="FAMILY_TYPE_VAL" property="familyTypeVal" javaType="java.lang.String"/>
            <result column="FAMILY_TYPE_KEY" property="familyTypeKey" javaType="java.lang.String"/>
            <result column="RELIEF_FUND" property="reliefFund" javaType="java.math.BigDecimal"/>
            <result column="CLASSIFY_FUND" property="classifyFund" javaType="java.math.BigDecimal"/>
            <result column="TOTAL_FUND" property="totalFund" javaType="java.math.BigDecimal"/>
            <result column="RESIDENCE_CODE" property="residenceCode" javaType="java.lang.String"/>
            <result column="RESIDENCE_ADDRESS" property="residenceAddress" javaType="java.lang.String"/>
            <result column="DOMICILE_CODE" property="domicileCode" javaType="java.lang.String"/>
            <result column="DOMICILE_ADRESS" property="domicileAdress" javaType="java.lang.String"/>
            <result column="REGISTER_LOCATION" property="registerLocation" javaType="java.lang.Integer"/>
            <result column="IS_FIND" property="isFind" javaType="java.lang.Integer"/>
            <result column="LONGITUDE" property="longitude" javaType="java.math.BigDecimal"/>
            <result column="LATITUDE" property="latitude" javaType="java.math.BigDecimal"/>
            <result column="POSITION_ADDRESS" property="positionAddress" javaType="java.lang.String"/>
            <result column="GROUP_NAME" property="groupName" javaType="java.lang.String"/>
            <result column="APPLY_DATE" property="applyDate" javaType="java.util.Date"/>
            <result column="REASON" property="reason" javaType="java.lang.String"/>
            <result column="NEXT_PLAN" property="nextPlan" javaType="java.lang.String"/>
            <result column="HELP_PLAN" property="helpPlan" javaType="java.lang.String"/>
            <result column="FIRST_START_DATE" property="firstStartDate" javaType="java.util.Date"/>
            <result column="CURRENT_START_DATE" property="currentStartDate" javaType="java.util.Date"/>
            <result column="END_DATE" property="endDate" javaType="java.util.Date"/>
            <result column="OPERATION_CATEGORY" property="operationCategory" javaType="java.lang.Integer"/>
            <result column="OPERATION_REASON" property="operationReason" javaType="java.lang.Integer"/>
            <result column="AUDIT_STATUS" property="auditStatus" javaType="java.lang.Integer"/>
            <result column="DATA_STATUS" property="dataStatus" javaType="java.lang.Integer"/>
            <result column="EFFECTIVE_STATUS" property="effectiveStatus" javaType="java.lang.Integer"/>
            <result column="FLOW_LIMIT_DAY" property="flowLimitDay" javaType="java.lang.Integer"/>
            <result column="FLOW_STATUS" property="flowStatus" javaType="java.lang.Integer"/>
            <result column="FLOW_INSTANCE_ID" property="flowInstanceId" javaType="java.lang.String"/>
            <result column="DESCRIPTION" property="description" javaType="java.lang.String"/>
            <result column="IS_PERFECT" property="isPerfect" javaType="java.lang.Integer"/>
            <result column="CHECK_STATE" property="checkState" javaType="java.lang.Integer"/>
            <result column="IS_WARNING" property="isWarning" javaType="java.lang.Integer"/>
            <result column="SUPPLY_STATUS" property="supplyStatus" javaType="java.lang.Integer"/>
            <result column="DATA_SOURCE" property="dataSource" javaType="java.lang.Integer"/>
            <result column="DATA_SOURCE_TYPE" property="dataSourceType" javaType="java.lang.Integer"/>
            <result column="IS_ALL_HELP" property="isAllHelp" javaType="java.lang.Integer"/>
            <result column="IS_READ" property="isRead" javaType="java.lang.Integer"/>
            <result column="PRE_FAMILY_ID" property="preFamilyId" javaType="java.lang.String"/>
            <result column="SUBSISTENCE_EDGE_VAL" property="subsistenceEdgeVal" javaType="java.lang.Integer"/>
            <result column="SUBSISTENCE_EDGE_KEY" property="subsistenceEdgeKey" javaType="java.lang.String"/>
            <result column="CREATE_ORG_CODE" property="createOrgCode" javaType="java.lang.String"/>
            <result column="CREATE_ORG_NAME" property="createOrgName" javaType="java.lang.String"/>
            <result column="DELETE_MARK" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="CREATE_DATE" property="createDate" javaType="java.util.Date"/>
            <result column="CREATE_USER_ID" property="createUserId" javaType="java.lang.String"/>
            <result column="CREATE_USER_NAME" property="createUserName" javaType="java.lang.String"/>
            <result column="MODIFY_DATE" property="modifyDate" javaType="java.util.Date"/>
            <result column="MODIFY_USER_ID" property="modifyUserId" javaType="java.lang.String"/>
            <result column="MODIFY_USER_NAME" property="modifyUserName" javaType="java.lang.String"/>
            <result column="SUBSIDY_STANDARD" property="subsidyStandard" javaType="java.math.BigDecimal"/>
            <result column="SINGLE_INSURANCE_VAL" property="singleInsuranceVal" javaType="java.lang.Integer"/>
            <result column="EFFECTIVE_DATE" property="effectiveDate" javaType="java.util.Date"/>
            <result column="REVIEW_STATUS" property="reviewStatus" javaType="java.lang.Integer"/>
    </resultMap>
</mapper>