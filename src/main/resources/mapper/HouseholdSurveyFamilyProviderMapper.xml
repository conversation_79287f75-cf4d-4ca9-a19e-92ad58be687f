<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyFamilyProviderMapper">

    <resultMap id="HouseholdSurveyFamilyProviderMap"
               type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyFamilyProvider">
        <result column="id" property="id" javaType="java.lang.String"/>
        <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
        <result column="create_date" property="createDate" javaType="java.util.Date"/>
        <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
        <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
        <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
        <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
        <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
        <result column="household_survey_id" property="householdSurveyId" javaType="java.lang.String"/>
        <result column="name" property="name" javaType="java.lang.String"/>
        <result column="id_card" property="idCard" javaType="java.lang.String"/>
        <result column="age" property="age" javaType="java.lang.Integer"/>
        <result column="sex" property="sex" javaType="java.lang.Integer"/>
        <result column="nationality_val" property="nationalityVal" javaType="java.lang.String"/>
        <result column="nationality_key" property="nationalityKey" javaType="java.lang.String"/>
        <result column="relation_applicant_key" property="relationApplicantKey" javaType="java.lang.String"/>
        <result column="RELATION_APPLICANT_VAL" property="relationApplicantVal" javaType="java.lang.String"/>
        <result column="body_condition_val" property="bodyConditionVal" javaType="java.lang.String"/>
        <result column="body_condition_key" property="bodyConditionKey" javaType="java.lang.String"/>
        <result column="marital_status_val" property="maritalStatusVal" javaType="java.lang.String"/>
        <result column="marital_status_key" property="maritalStatusKey" javaType="java.lang.String"/>
        <result column="disability_category_level_val" property="disabilityCategoryLevelVal"
                javaType="java.lang.String"/>
        <result column="disability_category_level_key" property="disabilityCategoryLevelKey"
                javaType="java.lang.String"/>
        <result column="disability_category_val" property="disabilityCategoryVal" javaType="java.lang.String"/>
        <result column="disability_category_key" property="disabilityCategoryKey" javaType="java.lang.String"/>
        <result column="disease_category_val" property="diseaseCategoryVal" javaType="java.lang.String"/>
        <result column="disease_category_key" property="diseaseCategoryKey" javaType="java.lang.String"/>
        <result column="work_status_val" property="workStatusVal" javaType="java.lang.String"/>
        <result column="work_status_key" property="workStatusKey" javaType="java.lang.String"/>
        <result column="monthly_income" property="monthlyIncome" javaType="java.math.BigDecimal"/>
        <result column="monthly_child_support" property="monthlyChildSupport" javaType="java.math.BigDecimal"/>
        <result column="is_has_support_ability" property="isHasSupportAbility" javaType="java.lang.Integer"/>
        <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
        <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
        <result column="IS_DISABLED" property="isDisabled" javaType="java.lang.Integer"/>
        <result column="IS_SERIOUSLY" property="isSeriously" javaType="java.lang.Integer"/>
    </resultMap>
</mapper>