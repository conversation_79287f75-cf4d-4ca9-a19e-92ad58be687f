<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org/DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.jqsoft.zhmz.cloudplatform.msgs.mapper.HouseholdSurveyFamilyPropertyMapper">

    <resultMap id="HouseholdSurveyFamilyPropertyMap" type="net.jqsoft.zhmz.cloudplatform.msgs.model.entity.HouseholdSurveyFamilyProperty">
            <result column="id" property="id" javaType="java.lang.String"/>
            <result column="delete_mark" property="deleteMark" javaType="java.lang.Integer"/>
            <result column="create_date" property="createDate" javaType="java.util.Date"/>
            <result column="create_user_id" property="createUserId" javaType="java.lang.String"/>
            <result column="create_user_name" property="createUserName" javaType="java.lang.String"/>
            <result column="modify_date" property="modifyDate" javaType="java.util.Date"/>
            <result column="modify_user_id" property="modifyUserId" javaType="java.lang.String"/>
            <result column="modify_user_name" property="modifyUserName" javaType="java.lang.String"/>
            <result column="household_survey_id" property="householdSurveyId" javaType="java.lang.String"/>
            <result column="property_category_val" property="propertyCategoryVal" javaType="java.lang.String"/>
            <result column="property_category_key" property="propertyCategoryKey" javaType="java.lang.String"/>
            <result column="amount" property="amount" javaType="java.math.BigDecimal"/>
            <result column="description" property="description" javaType="java.lang.String"/>
            <result column="related_table_id" property="relatedTableId" javaType="java.lang.String"/>
            <result column="related_table_name" property="relatedTableName" javaType="java.lang.String"/>
    </resultMap>
</mapper>