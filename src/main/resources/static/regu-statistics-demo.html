<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监管统计示例页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .overview-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .overview-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .overview-card .number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .chart-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .todo-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        .todo-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #4CAF50;
        }
        .feedback-rate {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .rate-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#4CAF50 0deg 180deg, #e0e0e0 180deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
        }
        .rate-circle::before {
            content: '';
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }
        .rate-text {
            position: relative;
            z-index: 1;
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #f44336;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>监管统计数据展示</h1>
        
        <div style="margin-bottom: 20px;">
            <button onclick="loadAllStatistics()">加载统计数据</button>
            <button onclick="loadOverview()">加载概况</button>
            <button onclick="loadBusinessAnalysis()">加载业务分析</button>
            <button onclick="loadTodoStatistics()">加载待办统计</button>
        </div>

        <!-- 监管概况 -->
        <div class="overview-cards" id="overview-section">
            <div class="overview-card">
                <h3>监管概况</h3>
                <div class="number" id="total-count">-</div>
                <div>今日新增 4 条</div>
            </div>
            <div class="overview-card">
                <h3>超期数据总数</h3>
                <div class="number" id="overdue-count">-</div>
                <div>今日新增 4 条</div>
            </div>
            <div class="overview-card">
                <h3>待办办理总数</h3>
                <div class="number" id="pending-count">-</div>
                <div>今日新增 4 条</div>
            </div>
            <div class="overview-card">
                <h3>平均反馈率</h3>
                <div class="number" id="avg-feedback-rate">-</div>
                <div>↑ 0.6% 较昨日</div>
            </div>
        </div>

        <!-- 业务分析图表 -->
        <div class="chart-container">
            <div class="card">
                <h3>监管预警业务分析</h3>
                <div id="business-analysis">
                    <div class="loading">点击"加载业务分析"按钮查看数据</div>
                </div>
            </div>
            <div class="card">
                <h3>督办预警业务分析</h3>
                <div id="supervision-analysis">
                    <div class="loading">点击"加载统计数据"按钮查看数据</div>
                </div>
            </div>
        </div>

        <!-- 待办事项 -->
        <div class="card">
            <h3>待办事项</h3>
            <div class="todo-grid" id="todo-section">
                <div class="todo-item">
                    <div style="font-size: 24px; font-weight: bold; color: #2196F3;" id="province-todo">-</div>
                    <div>省级待办（个）</div>
                </div>
                <div class="todo-item">
                    <div style="font-size: 24px; font-weight: bold; color: #2196F3;" id="city-todo">-</div>
                    <div>市级待办（个）</div>
                </div>
                <div class="todo-item">
                    <div style="font-size: 24px; font-weight: bold; color: #2196F3;" id="county-todo">-</div>
                    <div>县级待办（个）</div>
                </div>
                <div class="todo-item">
                    <div style="font-size: 24px; font-weight: bold; color: #FF9800;" id="town-todo">-</div>
                    <div>乡镇待办（个）</div>
                </div>
                <div class="todo-item">
                    <div style="font-size: 24px; font-weight: bold; color: #FF9800;" id="village-todo">-</div>
                    <div>村级待办（个）</div>
                </div>
                <div class="todo-item">
                    <div style="font-size: 24px; font-weight: bold; color: #FF9800;" id="other-todo">-</div>
                    <div>其他（个）</div>
                </div>
            </div>
        </div>

        <!-- 平均反馈率 -->
        <div class="card">
            <h3>平均反馈率</h3>
            <div class="feedback-rate">
                <div style="text-align: center;">
                    <div class="rate-circle">
                        <div class="rate-text" id="business-rate">50%</div>
                    </div>
                    <h4>业务办理反馈率</h4>
                    <p>共反馈 <span id="business-feedback-count">-</span> 条，总计 <span id="business-total-count">-</span> 条</p>
                </div>
                <div style="text-align: center;">
                    <div class="rate-circle">
                        <div class="rate-text" id="supervision-rate">50%</div>
                    </div>
                    <h4>督办反馈率</h4>
                    <p>共反馈 <span id="supervision-feedback-count">-</span> 条，总计 <span id="supervision-total-count">-</span> 条</p>
                </div>
            </div>
        </div>

        <div id="error-message"></div>
    </div>

    <script>
        // API 基础路径
        const API_BASE = '/reguStatistics';

        // 显示错误信息
        function showError(message) {
            document.getElementById('error-message').innerHTML = 
                `<div class="error">错误: ${message}</div>`;
        }

        // 清除错误信息
        function clearError() {
            document.getElementById('error-message').innerHTML = '';
        }

        // 加载完整统计数据
        async function loadAllStatistics() {
            clearError();
            try {
                const response = await fetch(`${API_BASE}/overview`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const result = await response.json();
                
                if (result.success && result.data) {
                    updateOverview(result.data.overview);
                    updateBusinessAnalysis(result.data.businessAnalysis);
                    updateSupervisionAnalysis(result.data.supervisionAnalysis);
                    updateTodoStatistics(result.data.todoStatistics);
                    updateFeedbackRate(result.data.feedbackRateStatistics);
                } else {
                    showError(result.message || '获取数据失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            }
        }

        // 加载概况统计
        async function loadOverview() {
            clearError();
            try {
                const response = await fetch(`${API_BASE}/overview/summary`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const result = await response.json();
                
                if (result.success && result.data) {
                    updateOverview(result.data);
                } else {
                    showError(result.message || '获取概况数据失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            }
        }

        // 加载业务分析
        async function loadBusinessAnalysis() {
            clearError();
            try {
                const response = await fetch(`${API_BASE}/business/analysis`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const result = await response.json();
                
                if (result.success && result.data) {
                    updateBusinessAnalysis(result.data);
                } else {
                    showError(result.message || '获取业务分析数据失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            }
        }

        // 加载待办统计
        async function loadTodoStatistics() {
            clearError();
            try {
                const response = await fetch(`${API_BASE}/todo/statistics`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const result = await response.json();
                
                if (result.success && result.data) {
                    updateTodoStatistics(result.data);
                } else {
                    showError(result.message || '获取待办统计数据失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            }
        }

        // 更新概况数据
        function updateOverview(data) {
            if (data) {
                document.getElementById('total-count').textContent = data.totalCount || 0;
                document.getElementById('overdue-count').textContent = data.overdueCount || 0;
                document.getElementById('pending-count').textContent = data.pendingCount || 0;
                document.getElementById('avg-feedback-rate').textContent =
                    (data.avgFeedbackRate ? data.avgFeedbackRate + '%' : '0%');
            }
        }

        // 更新业务分析数据
        function updateBusinessAnalysis(data) {
            const container = document.getElementById('business-analysis');
            if (data && data.categoryList) {
                let html = `<div>总计: ${data.totalCount || 0} 条</div><ul>`;
                data.categoryList.forEach(item => {
                    html += `<li>${item.categoryName}: ${item.count} 条 (${item.percentage || 0}%)</li>`;
                });
                html += '</ul>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<div class="loading">暂无数据</div>';
            }
        }

        // 更新督办分析数据
        function updateSupervisionAnalysis(data) {
            const container = document.getElementById('supervision-analysis');
            if (data && data.categoryList) {
                let html = `<div>总计: ${data.totalCount || 0} 条</div><ul>`;
                data.categoryList.forEach(item => {
                    html += `<li>${item.categoryName}: ${item.count} 条 (${item.percentage || 0}%)</li>`;
                });
                html += '</ul>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<div class="loading">暂无数据</div>';
            }
        }

        // 更新待办统计数据
        function updateTodoStatistics(data) {
            if (data) {
                document.getElementById('province-todo').textContent = data.provinceTodo || 0;
                document.getElementById('city-todo').textContent = data.cityTodo || 0;
                document.getElementById('county-todo').textContent = data.countyTodo || 0;
                document.getElementById('town-todo').textContent = data.townTodo || 0;
                document.getElementById('village-todo').textContent = data.villageTodo || 0;
                document.getElementById('other-todo').textContent = data.otherTodo || 0;
            }
        }

        // 更新反馈率数据
        function updateFeedbackRate(data) {
            if (data) {
                if (data.businessFeedbackRate) {
                    document.getElementById('business-rate').textContent =
                        (data.businessFeedbackRate.rate || 0) + '%';
                    document.getElementById('business-feedback-count').textContent = 
                        data.businessFeedbackRate.feedbackCount || 0;
                    document.getElementById('business-total-count').textContent = 
                        data.businessFeedbackRate.totalCount || 0;
                }
                
                if (data.supervisionFeedbackRate) {
                    document.getElementById('supervision-rate').textContent =
                        (data.supervisionFeedbackRate.rate || 0) + '%';
                    document.getElementById('supervision-feedback-count').textContent = 
                        data.supervisionFeedbackRate.feedbackCount || 0;
                    document.getElementById('supervision-total-count').textContent = 
                        data.supervisionFeedbackRate.totalCount || 0;
                }
            }
        }

        // 页面加载完成后自动加载数据
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，可以点击按钮加载统计数据');
        });
    </script>
</body>
</html>
