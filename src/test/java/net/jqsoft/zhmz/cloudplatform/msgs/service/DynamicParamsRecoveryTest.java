package net.jqsoft.zhmz.cloudplatform.msgs.service;

import lombok.extern.slf4j.Slf4j;
import net.jqsoft.zhmz.cloudplatform.msgs.model.entity.MsgsSignaturehistory;
import net.jqsoft.zhmz.cloudplatform.msgs.model.vo.ElectronicSignatureVO;
import net.jqsoft.zhmz.cloudplatform.msgs.service.impl.MsgsSignaturehistoryServiceImpl;
import net.jqsoft.zhmz.cloudplatform.msgs.utils.DynamicParamsValidator;
import net.jqsoft.zhmz.common.vo.UserVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 动态参数恢复功能测试
 * 验证电子签章过程中动态参数的正确传递和保存
 *
 * <AUTHOR>
 * @date 2025-09-25
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DynamicParamsRecoveryTest {

    private Map<String, Object> testParams;
    private String testFamilyId;
    private String testTemplateId;
    private String testFileType;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testFamilyId = "test_family_001";
        testTemplateId = "dibao_approval";
        testFileType = "1";

        testParams = new HashMap<>();
        testParams.put("outReason", "户籍迁移");
        testParams.put("auditDesc", "经审核符合条件");
        testParams.put("adjustReason", "金额调整");
        testParams.put("customParam1", "自定义参数1");
        testParams.put("customParam2", "自定义参数2");
    }

    @Test
    void testDynamicParamsValidation() {
        log.info("开始测试动态参数验证功能");

        // 测试有效参数
        DynamicParamsValidator.ValidationResult result = 
                DynamicParamsValidator.validateParams(testParams, "测试验证");
        
        assertFalse(result.hasErrors(), "有效参数不应该有错误");
        log.info("有效参数验证通过：{}", result.getSummary());

        // 测试无效参数
        Map<String, Object> invalidParams = new HashMap<>();
        invalidParams.put("", "空键名");
        invalidParams.put("validKey", null);
        invalidParams.put("stampDate", "invalid-date-format");

        DynamicParamsValidator.ValidationResult invalidResult = 
                DynamicParamsValidator.validateParams(invalidParams, "测试无效参数");
        
        assertTrue(invalidResult.hasErrors() || invalidResult.hasWarnings(), 
                "无效参数应该有错误或警告");
        log.info("无效参数验证结果：{}", invalidResult.getSummary());
    }

    @Test
    void testParameterComparison() {
        log.info("开始测试参数比较功能");

        Map<String, Object> oldParams = new HashMap<>();
        oldParams.put("outReason", "原因1");
        oldParams.put("auditDesc", "描述1");

        Map<String, Object> newParams = new HashMap<>();
        newParams.put("outReason", "原因2");  // 修改
        newParams.put("auditDesc", "描述1");  // 未修改
        newParams.put("adjustReason", "新增原因");  // 新增

        DynamicParamsValidator.ComparisonResult comparison = 
                DynamicParamsValidator.compareParams(oldParams, newParams, "测试比较");

        assertTrue(comparison.hasChanges(), "应该检测到参数变化");
        assertEquals(2, comparison.getChanges().size(), "应该有2个变化（1个修改，1个新增）");

        log.info("参数比较完成，变化数量：{}", comparison.getChanges().size());
        for (DynamicParamsValidator.ComparisonResult.ParameterChange change : comparison.getChanges()) {
            log.info("变化详情：参数[{}] {} - {} -> {}", 
                    change.getKey(), change.getChangeType(), change.getOldValue(), change.getNewValue());
        }
    }

    @Test
    void testMsgsSignaturehistoryParamsMethods() {
        log.info("开始测试MsgsSignaturehistory参数处理方法");

        MsgsSignaturehistory history = new MsgsSignaturehistory();
        
        // 测试设置业务参数
        history.setBusinessParamsFromMap(testParams);
        assertTrue(history.hasBusinessParams(), "应该有业务参数");
        
        Map<String, Object> retrievedParams = history.getBusinessParamsMap();
        assertEquals(testParams.size(), retrievedParams.size(), "参数数量应该一致");
        assertEquals(testParams.get("outReason"), retrievedParams.get("outReason"), "停保原因应该一致");

        // 测试设置预览参数
        history.setPreviewParamsFromMap(testParams);
        assertTrue(history.hasPreviewParams(), "应该有预览参数");
        
        Map<String, Object> retrievedPreviewParams = history.getPreviewParamsMap();
        assertEquals(testParams.size(), retrievedPreviewParams.size(), "预览参数数量应该一致");
        assertEquals(testParams.get("customParam1"), retrievedPreviewParams.get("customParam1"), "自定义参数应该一致");

        // 测试记录类型
        history.setIsPreviewRecord(MsgsSignaturehistory.RECORD_TYPE_PREVIEW);
        assertTrue(history.isPreviewRecord(), "应该是预览记录");

        history.setIsPreviewRecord(MsgsSignaturehistory.RECORD_TYPE_SIGNATURE);
        assertFalse(history.isPreviewRecord(), "应该不是预览记录");

        log.info("MsgsSignaturehistory参数处理方法测试完成");
    }

    @Test
    void testParameterJsonSerialization() {
        log.info("开始测试参数JSON序列化");

        MsgsSignaturehistory history = new MsgsSignaturehistory();
        
        // 测试包含特殊字符的参数
        Map<String, Object> specialParams = new HashMap<>();
        specialParams.put("reason", "包含\"引号\"的原因");
        specialParams.put("desc", "包含\n换行符的描述");
        specialParams.put("adjust", "包含\\反斜杠的调整");

        history.setBusinessParamsFromMap(specialParams);
        
        // 验证能够正确序列化和反序列化
        Map<String, Object> retrieved = history.getBusinessParamsMap();
        assertEquals(specialParams.size(), retrieved.size(), "特殊字符参数数量应该一致");
        assertEquals(specialParams.get("reason"), retrieved.get("reason"), "包含引号的参数应该正确处理");
        assertEquals(specialParams.get("desc"), retrieved.get("desc"), "包含换行符的参数应该正确处理");
        assertEquals(specialParams.get("adjust"), retrieved.get("adjust"), "包含反斜杠的参数应该正确处理");

        log.info("参数JSON序列化测试完成");
    }

    @Test
    void testEmptyAndNullParameters() {
        log.info("开始测试空参数和null参数处理");

        MsgsSignaturehistory history = new MsgsSignaturehistory();
        
        // 测试null参数
        history.setBusinessParamsFromMap(null);
        assertFalse(history.hasBusinessParams(), "null参数应该返回false");
        assertTrue(history.getBusinessParamsMap().isEmpty(), "null参数应该返回空Map");

        // 测试空参数
        history.setBusinessParamsFromMap(new HashMap<>());
        assertFalse(history.hasBusinessParams(), "空参数应该返回false");
        assertTrue(history.getBusinessParamsMap().isEmpty(), "空参数应该返回空Map");

        // 测试包含空值的参数
        Map<String, Object> paramsWithNulls = new HashMap<>();
        paramsWithNulls.put("validParam", "有效值");
        paramsWithNulls.put("nullParam", null);
        paramsWithNulls.put("emptyParam", "");
        paramsWithNulls.put("blankParam", "   ");

        history.setBusinessParamsFromMap(paramsWithNulls);
        Map<String, Object> retrieved = history.getBusinessParamsMap();
        
        // 应该只保存有效的参数
        assertEquals(1, retrieved.size(), "应该只保存有效参数");
        assertEquals("有效值", retrieved.get("validParam"), "有效参数应该被保存");
        assertNull(retrieved.get("nullParam"), "null参数不应该被保存");

        log.info("空参数和null参数处理测试完成");
    }

    @Test
    void testParameterValidationEdgeCases() {
        log.info("开始测试参数验证边界情况");

        // 测试超长参数
        Map<String, Object> longParams = new HashMap<>();
        StringBuilder longValue = new StringBuilder();
        for (int i = 0; i < 600; i++) {
            longValue.append("a");
        }
        longParams.put("outReason", longValue.toString());

        DynamicParamsValidator.ValidationResult result = 
                DynamicParamsValidator.validateParams(longParams, "测试超长参数");
        
        assertTrue(result.hasWarnings(), "超长参数应该产生警告");
        log.info("超长参数验证结果：{}", result.getSummary());

        // 测试日期格式参数
        Map<String, Object> dateParams = new HashMap<>();
        dateParams.put("stampDate", "2025-09-25");
        dateParams.put("invalidDate", "2025/09/25");

        DynamicParamsValidator.ValidationResult dateResult = 
                DynamicParamsValidator.validateParams(dateParams, "测试日期格式");
        
        assertTrue(dateResult.hasErrors(), "无效日期格式应该产生错误");
        log.info("日期格式验证结果：{}", dateResult.getSummary());
    }

    /**
     * 模拟完整的预览-签章流程测试
     * 这个测试需要在实际环境中运行，这里只是展示测试思路
     */
    @Test
    void testTraditionalBusinessParamsConversion() {
        log.info("开始测试传统业务参数转换为动态参数");

        // 模拟传统的业务参数
        String outReason = "户籍迁移";
        String auditDesc = "经审核符合条件";
        String adjustReason = "金额调整";

        // 使用工具类进行参数转换
        Map<String, Object> convertedParams = DynamicParamsValidator.convertTraditionalParams(outReason, auditDesc, adjustReason);

        // 验证转换结果
        assertEquals(3, convertedParams.size(), "应该转换3个传统业务参数");
        assertEquals(outReason, convertedParams.get("outReason"), "停保原因应该正确转换");
        assertEquals(auditDesc, convertedParams.get("auditDesc"), "审核描述应该正确转换");
        assertEquals(adjustReason, convertedParams.get("adjustReason"), "调整原因应该正确转换");

        log.info("传统业务参数转换测试完成，转换后的参数：{}", convertedParams);

        // 测试扩展版本的转换方法
        Map<String, Object> additionalParams = new HashMap<>();
        additionalParams.put("customParam", "自定义参数");
        additionalParams.put("stampDate", "2025-09-25");

        Map<String, Object> mergedParams = DynamicParamsValidator.convertTraditionalParams(
                outReason, auditDesc, adjustReason, additionalParams);

        assertEquals(5, mergedParams.size(), "合并后应该有5个参数");
        assertEquals("自定义参数", mergedParams.get("customParam"), "自定义参数应该正确合并");
        assertEquals("2025-09-25", mergedParams.get("stampDate"), "签章日期应该正确合并");

        log.info("扩展版本参数转换测试完成，合并后的参数：{}", mergedParams);
    }

    @Test
    void testCompletePreviewToSignatureFlow() {
        log.info("开始测试完整的预览-签章流程（模拟）");

        // 第一步：模拟预览生成，保存动态参数（包含传统业务参数）
        log.info("步骤1：生成预览并保存动态参数（包含传统业务参数）");
        log.info("预览参数：{}", testParams);

        // 第二步：模拟签章时参数恢复
        log.info("步骤2：签章时恢复动态参数");

        // 模拟从预览记录恢复参数的过程
        Map<String, Object> recoveredParams = new HashMap<>(testParams);
        log.info("恢复的参数：{}", recoveredParams);

        // 验证参数一致性
        assertEquals(testParams.size(), recoveredParams.size(), "恢复的参数数量应该一致");
        for (Map.Entry<String, Object> entry : testParams.entrySet()) {
            assertEquals(entry.getValue(), recoveredParams.get(entry.getKey()),
                    "参数[" + entry.getKey() + "]应该一致");
        }

        // 特别验证传统业务参数
        assertEquals("户籍迁移", recoveredParams.get("outReason"), "停保原因应该正确恢复");
        assertEquals("经审核符合条件", recoveredParams.get("auditDesc"), "审核描述应该正确恢复");
        assertEquals("金额调整", recoveredParams.get("adjustReason"), "调整原因应该正确恢复");

        log.info("完整流程测试完成，参数恢复成功，传统业务参数也正确恢复");
    }
}
